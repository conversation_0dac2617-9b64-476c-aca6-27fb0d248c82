'use client'

import { motion } from 'framer-motion'
import { 
  CpuChipIcon, 
  CheckCircleIcon, 
  ClockIcon, 
  BoltIcon 
} from '@heroicons/react/24/outline'
import { DashboardStats } from '@/types/dashboard'

interface StatsCardsProps {
  stats: DashboardStats | null
  isLoading: boolean
}

export function StatsCards({ stats, isLoading }: StatsCardsProps) {
  const cards = [
    {
      name: 'Active Crews',
      value: stats?.activeCrews || 0,
      icon: CpuChipIcon,
      color: 'emerald',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      name: 'Total Tasks',
      value: stats?.totalTasks || 0,
      icon: CheckCircleIcon,
      color: 'blue',
      change: '+19%',
      changeType: 'positive' as const,
    },
    {
      name: 'Success Rate',
      value: `${stats?.successRate || 0}%`,
      icon: CheckCircleIcon,
      color: 'green',
      change: '+2.1%',
      changeType: 'positive' as const,
    },
    {
      name: 'Avg Speed',
      value: `${stats?.averageSpeed || 0}s`,
      icon: BoltIcon,
      color: 'purple',
      change: '-0.3s',
      changeType: 'positive' as const,
    },
  ]

  const colorClasses: Record<string, string> = {
    emerald: 'text-emerald-600 bg-emerald-100',
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    purple: 'text-purple-600 bg-purple-100',
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-xl border border-slate-200 p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-slate-200 rounded w-20 mb-2"></div>
              <div className="h-8 bg-slate-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-slate-200 rounded w-12"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => (
        <motion.div
          key={card.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-xl border border-slate-200 p-6 hover:shadow-lg transition-shadow duration-200"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-slate-600">{card.name}</p>
              <p className="text-3xl font-bold text-slate-900 mt-2">{card.value}</p>
              <div className="flex items-center mt-2">
                <span className={`text-sm font-medium ${
                  card.changeType === 'positive' ? 'text-emerald-600' : 'text-red-600'
                }`}>
                  {card.change}
                </span>
                <span className="text-sm text-slate-500 ml-1">vs last month</span>
              </div>
            </div>
            <div className={`p-3 rounded-lg ${colorClasses[card.color]}`}>
              <card.icon className="h-6 w-6" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}
