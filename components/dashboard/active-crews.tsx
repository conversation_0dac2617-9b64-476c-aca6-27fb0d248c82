'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import {
  PlusIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  EllipsisVerticalIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline'
import { Menu } from '@headlessui/react'
import { useActiveCrews } from '@/lib/hooks/use-active-crews'
import { CrewStatus } from '@/types/crew'
import { clsx } from 'clsx'

export function ActiveCrews() {
  const { crews, isLoading } = useActiveCrews()

  const getStatusColor = (status: CrewStatus) => {
    switch (status) {
      case 'running':
        return 'bg-emerald-500'
      case 'pending':
        return 'bg-amber-500'
      case 'completed':
        return 'bg-slate-400'
      case 'failed':
        return 'bg-red-500'
      default:
        return 'bg-slate-400'
    }
  }

  const getStatusText = (status: CrewStatus) => {
    return status.charAt(0).toUpperCase() + status.slice(1)
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl border border-slate-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-slate-900">Active Crews</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-slate-200 rounded-lg h-40"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl border border-slate-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-slate-900">Active Crews</h3>
        <Link
          href="/crews/new"
          className="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          New Crew
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {crews.map((crew, index) => (
          <motion.div
            key={crew.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className="group relative bg-slate-50 rounded-lg p-4 hover:bg-slate-100 transition-colors cursor-pointer"
          >
            {/* Status Indicator */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className={clsx(
                  'h-3 w-3 rounded-full',
                  getStatusColor(crew.status),
                  crew.status === 'running' && 'animate-pulse'
                )}>
                </div>
                <span className="text-sm font-medium text-slate-600">
                  {getStatusText(crew.status)}
                </span>
              </div>
              
              <Menu as="div" className="relative">
                <Menu.Button className="p-1 rounded-md hover:bg-slate-200 transition-colors">
                  <EllipsisVerticalIcon className="h-4 w-4 text-slate-400" />
                </Menu.Button>
                <Menu.Items className="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <Menu.Item>
                    <button className="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                      View Details
                    </button>
                  </Menu.Item>
                  <Menu.Item>
                    <button className="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                      Edit
                    </button>
                  </Menu.Item>
                  <Menu.Item>
                    <button className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-slate-50">
                      Delete
                    </button>
                  </Menu.Item>
                </Menu.Items>
              </Menu>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-slate-200 rounded-full h-1 mb-4">
              <div 
                className={clsx(
                  'h-1 rounded-full transition-all duration-300',
                  crew.status === 'running' ? 'bg-primary-600' : 
                  crew.status === 'completed' ? 'bg-emerald-500' :
                  crew.status === 'failed' ? 'bg-red-500' : 'bg-amber-500'
                )}
                style={{ width: `${crew.progress}%` }}
              />
            </div>

            {/* Crew Info */}
            <div className="mb-4">
              <h4 className="text-base font-semibold text-slate-900 mb-2">
                {crew.name}
              </h4>
              <p className="text-sm text-slate-600 mb-2">
                {crew.agents?.length || 0} agents • {crew.tasks?.length || 0} tasks
              </p>
              {crew.model && (
                <span className="inline-flex items-center px-2 py-1 rounded-md bg-blue-100 text-blue-800 text-xs font-medium">
                  {crew.model}
                </span>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              {crew.status === 'running' ? (
                <button className="p-2 rounded-md bg-amber-100 text-amber-600 hover:bg-amber-200 transition-colors">
                  <PauseIcon className="h-4 w-4" />
                </button>
              ) : (
                <button className="p-2 rounded-md bg-emerald-100 text-emerald-600 hover:bg-emerald-200 transition-colors">
                  <PlayIcon className="h-4 w-4" />
                </button>
              )}
              <button className="p-2 rounded-md bg-red-100 text-red-600 hover:bg-red-200 transition-colors">
                <StopIcon className="h-4 w-4" />
              </button>
              
              <Link
                href={`/crews/${crew.id}`}
                className="ml-auto text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                View Details
              </Link>
            </div>

            {/* Hover Effect */}
            <div className="absolute inset-0 rounded-lg border-2 border-transparent group-hover:border-primary-200 transition-colors pointer-events-none" />
          </motion.div>
        ))}
      </div>

      {crews.length === 0 && (
        <div className="text-center py-12">
          <CpuChipIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-slate-900 mb-2">No active crews</h4>
          <p className="text-slate-600 mb-4">Get started by creating your first AI crew</p>
          <Link
            href="/crews/new"
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            Create Your First Crew
          </Link>
        </div>
      )}
    </div>
  )
}
