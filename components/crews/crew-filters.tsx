'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  CalendarIcon,
  CpuChipIcon,
  TagIcon,
  UserGroupIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface FilterState {
  dateRange: string
  models: string[]
  agentCount: string
  tags: string[]
}

export function CrewFilters() {
  const [filters, setFilters] = useState<FilterState>({
    dateRange: 'all',
    models: [],
    agentCount: 'all',
    tags: []
  })

  const availableModels = [
    'Cerebras Llama3.1-70B',
    'Cerebras Llama3.1-8B',
    'GPT-4',
    'GPT-3.5-turbo',
    'Claude-3'
  ]

  const availableTags = [
    'Research',
    'Content',
    'Analysis',
    'Marketing',
    'Development',
    'Support',
    'Sales',
    'Finance'
  ]

  const handleModelToggle = (model: string) => {
    setFilters(prev => ({
      ...prev,
      models: prev.models.includes(model)
        ? prev.models.filter(m => m !== model)
        : [...prev.models, model]
    }))
  }

  const handleTagToggle = (tag: string) => {
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }))
  }

  const clearAllFilters = () => {
    setFilters({
      dateRange: 'all',
      models: [],
      agentCount: 'all',
      tags: []
    })
  }

  const hasActiveFilters = filters.dateRange !== 'all' || 
                          filters.models.length > 0 || 
                          filters.agentCount !== 'all' || 
                          filters.tags.length > 0

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-6"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-slate-900">Advanced Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            Clear all
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Date Range */}
        <div className="space-y-3">
          <label className="flex items-center gap-2 text-sm font-medium text-slate-700">
            <CalendarIcon className="h-4 w-4" />
            Date Range
          </label>
          <select
            value={filters.dateRange}
            onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
          >
            <option value="all">All time</option>
            <option value="today">Today</option>
            <option value="week">This week</option>
            <option value="month">This month</option>
            <option value="quarter">This quarter</option>
            <option value="year">This year</option>
          </select>
        </div>

        {/* Agent Count */}
        <div className="space-y-3">
          <label className="flex items-center gap-2 text-sm font-medium text-slate-700">
            <UserGroupIcon className="h-4 w-4" />
            Agent Count
          </label>
          <select
            value={filters.agentCount}
            onChange={(e) => setFilters(prev => ({ ...prev, agentCount: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
          >
            <option value="all">Any number</option>
            <option value="1">1 agent</option>
            <option value="2-3">2-3 agents</option>
            <option value="4-5">4-5 agents</option>
            <option value="6+">6+ agents</option>
          </select>
        </div>

        {/* AI Models */}
        <div className="space-y-3">
          <label className="flex items-center gap-2 text-sm font-medium text-slate-700">
            <CpuChipIcon className="h-4 w-4" />
            AI Models
          </label>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {availableModels.map((model) => (
              <label key={model} className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={filters.models.includes(model)}
                  onChange={() => handleModelToggle(model)}
                  className="rounded border-slate-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-slate-700">{model}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-3">
          <label className="flex items-center gap-2 text-sm font-medium text-slate-700">
            <TagIcon className="h-4 w-4" />
            Tags
          </label>
          <div className="flex flex-wrap gap-2">
            {availableTags.map((tag) => (
              <button
                key={tag}
                onClick={() => handleTagToggle(tag)}
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  filters.tags.includes(tag)
                    ? 'bg-primary-100 text-primary-700 border border-primary-200'
                    : 'bg-slate-100 text-slate-600 border border-slate-200 hover:bg-slate-200'
                }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="pt-4 border-t border-slate-200">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm font-medium text-slate-700">Active filters:</span>
            
            {filters.dateRange !== 'all' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-primary-100 text-primary-700 rounded-md text-xs">
                Date: {filters.dateRange}
                <button
                  onClick={() => setFilters(prev => ({ ...prev, dateRange: 'all' }))}
                  className="hover:bg-primary-200 rounded-full p-0.5"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}

            {filters.agentCount !== 'all' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-primary-100 text-primary-700 rounded-md text-xs">
                Agents: {filters.agentCount}
                <button
                  onClick={() => setFilters(prev => ({ ...prev, agentCount: 'all' }))}
                  className="hover:bg-primary-200 rounded-full p-0.5"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}

            {filters.models.map((model) => (
              <span key={model} className="inline-flex items-center gap-1 px-2 py-1 bg-primary-100 text-primary-700 rounded-md text-xs">
                {model}
                <button
                  onClick={() => handleModelToggle(model)}
                  className="hover:bg-primary-200 rounded-full p-0.5"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            ))}

            {filters.tags.map((tag) => (
              <span key={tag} className="inline-flex items-center gap-1 px-2 py-1 bg-primary-100 text-primary-700 rounded-md text-xs">
                #{tag}
                <button
                  onClick={() => handleTagToggle(tag)}
                  className="hover:bg-primary-200 rounded-full p-0.5"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        </div>
      )}
    </motion.div>
  )
}
