'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { formatDistanceToNow } from 'date-fns'
import { 
  PlayIcon, 
  PauseIcon, 
  StopIcon,
  EllipsisVerticalIcon,
  CpuChipIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  DocumentDuplicateIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { Menu } from '@headlessui/react'
import { Crew } from '@/types/crew'
import { clsx } from 'clsx'

interface CrewCardProps {
  crew: Crew
  viewMode: 'grid' | 'list'
}

export function CrewCard({ crew, viewMode }: CrewCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <PlayIcon className="h-4 w-4" />
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'failed':
        return <XCircleIcon className="h-4 w-4" />
      case 'pending':
        return <ClockIcon className="h-4 w-4" />
      default:
        return <CpuChipIcon className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-emerald-600 bg-emerald-100'
      case 'completed':
        return 'text-blue-600 bg-blue-100'
      case 'failed':
        return 'text-red-600 bg-red-100'
      case 'pending':
        return 'text-amber-600 bg-amber-100'
      case 'draft':
        return 'text-slate-600 bg-slate-100'
      default:
        return 'text-slate-600 bg-slate-100'
    }
  }

  const getProgressColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-emerald-500'
      case 'completed':
        return 'bg-blue-500'
      case 'failed':
        return 'bg-red-500'
      case 'pending':
        return 'bg-amber-500'
      default:
        return 'bg-slate-300'
    }
  }

  if (viewMode === 'list') {
    return (
      <motion.div
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className="bg-white rounded-xl border border-slate-200 p-6 hover:shadow-lg transition-all duration-200"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            {/* Status Indicator */}
            <div className={clsx(
              'flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium',
              getStatusColor(crew.status)
            )}>
              {getStatusIcon(crew.status)}
              {crew.status.charAt(0).toUpperCase() + crew.status.slice(1)}
            </div>

            {/* Crew Info */}
            <div className="flex-1 min-w-0">
              <Link href={`/crews/${crew.id}`} className="group">
                <h3 className="text-lg font-semibold text-slate-900 group-hover:text-primary-600 transition-colors">
                  {crew.name}
                </h3>
              </Link>
              <p className="text-sm text-slate-600 mt-1 line-clamp-1">
                {crew.description}
              </p>
              <div className="flex items-center gap-4 mt-2 text-xs text-slate-500">
                <span>{crew.agents?.length || 0} agents</span>
                <span>{crew.tasks?.length || 0} tasks</span>
                <span>Updated {formatDistanceToNow(new Date(crew.updated_at), { addSuffix: true })}</span>
              </div>
            </div>

            {/* Progress */}
            <div className="w-32">
              <div className="flex items-center justify-between text-sm mb-1">
                <span className="text-slate-600">Progress</span>
                <span className="font-medium">{crew.progress}%</span>
              </div>
              <div className="w-full bg-slate-200 rounded-full h-2">
                <div 
                  className={clsx('h-2 rounded-full transition-all duration-300', getProgressColor(crew.status))}
                  style={{ width: `${crew.progress}%` }}
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 ml-4">
            {crew.status === 'running' ? (
              <button className="p-2 rounded-md bg-amber-100 text-amber-600 hover:bg-amber-200 transition-colors">
                <PauseIcon className="h-4 w-4" />
              </button>
            ) : (
              <button className="p-2 rounded-md bg-emerald-100 text-emerald-600 hover:bg-emerald-200 transition-colors">
                <PlayIcon className="h-4 w-4" />
              </button>
            )}
            
            <Menu as="div" className="relative">
              <Menu.Button className="p-2 rounded-md hover:bg-slate-100 transition-colors">
                <EllipsisVerticalIcon className="h-4 w-4 text-slate-400" />
              </Menu.Button>
              <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <Menu.Item>
                  <Link href={`/crews/${crew.id}`} className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                    View Details
                  </Link>
                </Menu.Item>
                <Menu.Item>
                  <Link href={`/crews/${crew.id}/edit`} className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                    Edit Crew
                  </Link>
                </Menu.Item>
                <Menu.Item>
                  <button className="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                    Duplicate
                  </button>
                </Menu.Item>
                <Menu.Item>
                  <button className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-slate-50">
                    Delete
                  </button>
                </Menu.Item>
              </Menu.Items>
            </Menu>
          </div>
        </div>
      </motion.div>
    )
  }

  // Grid view
  return (
    <motion.div
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="bg-white rounded-xl border border-slate-200 p-6 hover:shadow-lg transition-all duration-200 group"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className={clsx(
          'flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium',
          getStatusColor(crew.status)
        )}>
          {getStatusIcon(crew.status)}
          {crew.status.charAt(0).toUpperCase() + crew.status.slice(1)}
        </div>
        
        <Menu as="div" className="relative">
          <Menu.Button className="p-1 rounded-md hover:bg-slate-100 transition-colors opacity-0 group-hover:opacity-100">
            <EllipsisVerticalIcon className="h-4 w-4 text-slate-400" />
          </Menu.Button>
          <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            <Menu.Item>
              <Link href={`/crews/${crew.id}`} className="flex items-center gap-2 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                <CpuChipIcon className="h-4 w-4" />
                View Details
              </Link>
            </Menu.Item>
            <Menu.Item>
              <Link href={`/crews/${crew.id}/edit`} className="flex items-center gap-2 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                <PencilIcon className="h-4 w-4" />
                Edit Crew
              </Link>
            </Menu.Item>
            <Menu.Item>
              <button className="flex items-center gap-2 w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                <DocumentDuplicateIcon className="h-4 w-4" />
                Duplicate
              </button>
            </Menu.Item>
            <Menu.Item>
              <button className="flex items-center gap-2 w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-slate-50">
                <TrashIcon className="h-4 w-4" />
                Delete
              </button>
            </Menu.Item>
          </Menu.Items>
        </Menu>
      </div>

      {/* Content */}
      <div className="mb-4">
        <Link href={`/crews/${crew.id}`} className="group">
          <h3 className="text-lg font-semibold text-slate-900 group-hover:text-primary-600 transition-colors mb-2">
            {crew.name}
          </h3>
        </Link>
        <p className="text-sm text-slate-600 line-clamp-2 mb-3">
          {crew.description}
        </p>
        
        <div className="flex items-center gap-4 text-xs text-slate-500 mb-3">
          <span>{crew.agents?.length || 0} agents</span>
          <span>{crew.tasks?.length || 0} tasks</span>
          {crew.model && (
            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md font-medium">
              {crew.model}
            </span>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm mb-2">
            <span className="text-slate-600">Progress</span>
            <span className="font-medium">{crew.progress}%</span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div 
              className={clsx('h-2 rounded-full transition-all duration-300', getProgressColor(crew.status))}
              style={{ width: `${crew.progress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-slate-100">
        <span className="text-xs text-slate-500">
          Updated {formatDistanceToNow(new Date(crew.updated_at), { addSuffix: true })}
        </span>
        
        <div className="flex items-center gap-2">
          {crew.status === 'running' ? (
            <button className="p-2 rounded-md bg-amber-100 text-amber-600 hover:bg-amber-200 transition-colors">
              <PauseIcon className="h-4 w-4" />
            </button>
          ) : crew.status === 'draft' ? (
            <Link 
              href={`/crews/${crew.id}/edit`}
              className="p-2 rounded-md bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
            >
              <PencilIcon className="h-4 w-4" />
            </Link>
          ) : (
            <button className="p-2 rounded-md bg-emerald-100 text-emerald-600 hover:bg-emerald-200 transition-colors">
              <PlayIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </motion.div>
  )
}
