# CrewCraft AI Platform - Complete Codebase Extraction

This document contains all the code files extracted from the repository in a GitHub-like format.

## Project Structure

```
crewcraft-ai-platform/
├── package.json
├── tailwind.config.js
├── postcss.config.js
├── next.config.js
├── tsconfig.json
├── .env.local.example
├── app/
│   ├── layout.tsx
│   ├── globals.css
│   └── page.tsx
├── components/
│   ├── layouts/
│   │   └── dashboard-layout.tsx
│   ├── navigation/
│   │   ├── sidebar.tsx
│   │   ├── header.tsx
│   │   └── mobile-menu.tsx
│   ├── dashboard/
│   │   ├── stats-cards.tsx
│   │   ├── active-crews.tsx
│   │   ├── live-preview.tsx
│   │   ├── quick-actions.tsx
│   │   └── activity-feed.tsx
│   ├── crews/
│   │   ├── crew-form.tsx
│   │   ├── crew-card.tsx
│   │   ├── crew-details.tsx
│   │   └── agent-builder.tsx
│   ├── templates/
│   │   ├── template-gallery.tsx
│   │   ├── template-card.tsx
│   │   └── template-preview.tsx
│   ├── ui/
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── modal.tsx
│   │   ├── loading.tsx
│   │   └── toast.tsx
│   └── providers.tsx
├── lib/
│   ├── supabase.ts
│   ├── cerebras.ts
│   ├── utils.ts
│   ├── validations.ts
│   └── hooks/
│       ├── use-dashboard-stats.ts
│       ├── use-active-crews.ts
│       ├── use-live-preview.ts
│       └── use-crews.ts
├── types/
│   ├── dashboard.ts
│   ├── crew.ts
│   ├── agent.ts
│   └── template.ts
└── preview/
    └── wireframe.svg
```

## Configuration Files

### package.json
```json
{
  "name": "crewcraft-ai-platform",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "next": "14.0.4",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.3.3",
    "@types/node": "^20.10.5",
    "@types/react": "^18.2.45",
    "@types/react-dom": "^18.2.18",
    "tailwindcss": "^3.4.0",
    "postcss": "^8.4.32",
    "autoprefixer": "^10.4.16",
    "@headlessui/react": "^1.7.17",
    "@heroicons/react": "^2.0.18",
    "framer-motion": "^10.16.16",
    "lucide-react": "^0.303.0",
    "recharts": "^2.8.0",
    "@supabase/supabase-js": "^2.38.5",
    "axios": "^1.6.2",
    "react-hot-toast": "^2.4.1",
    "date-fns": "^3.0.6",
    "uuid": "^9.0.1",
    "@types/uuid": "^9.0.7",
    "react-syntax-highlighter": "^15.5.0",
    "@types/react-syntax-highlighter": "^15.5.11",
    "react-hook-form": "^7.48.2",
    "@hookform/resolvers": "^3.3.2",
    "zod": "^3.22.4",
    "clsx": "^2.0.0",
    "class-variance-authority": "^0.7.0"
  },
  "devDependencies": {
    "eslint": "^8.56.0",
    "eslint-config-next": "14.0.4",
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10"
  }
}
```

### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          900: '#312e81',
        },
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        emerald: {
          500: '#10b981',
          600: '#059669',
        },
        amber: {
          500: '#f59e0b',
          600: '#d97706',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Menlo', 'monospace'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-subtle': 'bounce 2s infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

### postcss.config.js
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['images.unsplash.com', 'avatars.githubusercontent.com'],
  },
  async rewrites() {
    return [
      {
        source: '/api/crews/:path*',
        destination: '/api/crews/:path*',
      },
    ]
  },
}

module.exports = nextConfig
```

### tsconfig.json
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*"],
      "@/lib/*": ["./lib/*"],
      "@/types/*": ["./types/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### .env.local.example
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Cerebras API Configuration
CEREBRAS_API_KEY=csk-your_cerebras_api_key

# Application Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Optional: Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_vercel_analytics_id
```

## App Directory

### app/layout.tsx
```tsx
import type { Metadata } from 'next'
import { Inter, JetBrains_Mono } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
})

export const metadata: Metadata = {
  title: 'CrewCraft AI Platform - Multi-Agent AI Orchestration',
  description: 'Create, manage, and deploy AI agent crews with Cerebras ultra-fast inference. Build sophisticated multi-agent workflows for complex tasks.',
  keywords: 'AI, CrewAI, Cerebras, Multi-Agent, Artificial Intelligence, Automation, Workflows',
  authors: [{ name: 'CrewCraft Team' }],
  openGraph: {
    title: 'CrewCraft AI Platform',
    description: 'Enterprise-grade multi-agent AI orchestration platform',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${jetbrainsMono.variable}`}>
      <body className="font-sans antialiased bg-slate-50 text-slate-800">
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              className: 'bg-white shadow-lg border border-slate-200',
            }}
          />
        </Providers>
      </body>
    </html>
  )
}
```

### app/globals.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap');

@layer base {
  :root {
    --font-inter: 'Inter', system-ui, sans-serif;
    --font-jetbrains-mono: 'JetBrains Mono', 'Menlo', monospace;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  * {
    border-color: theme('colors.slate.200');
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .gradient-success {
    @apply bg-gradient-to-r from-emerald-500 to-emerald-600;
  }

  .gradient-warning {
    @apply bg-gradient-to-r from-amber-500 to-amber-600;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px -10px theme('colors.primary.500');
    }
    to {
      box-shadow: 0 0 20px -5px theme('colors.primary.500'), 0 0 40px -10px theme('colors.primary.300');
    }
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .code-highlight {
    @apply bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-slate-100 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-slate-300 rounded-full hover:bg-slate-400;
  }

  /* Loading animations */
  .loading-dots {
    display: inline-block;
  }

  .loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
  }

  @keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }

  /* Status indicators */
  .status-running {
    @apply relative;
  }

  .status-running::before {
    content: '';
    @apply absolute -inset-1 bg-emerald-400 rounded-full animate-ping;
  }

  .status-pending {
    @apply relative;
  }

  .status-pending::before {
    content: '';
    @apply absolute -inset-1 bg-amber-400 rounded-full animate-pulse;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent;
  }

  .shadow-glow {
    box-shadow: 0 0 30px -5px theme('colors.primary.500/50');
  }

  .border-gradient {
    @apply border-2 border-transparent bg-gradient-to-r from-primary-500 to-primary-600 bg-origin-border;
  }
}
```

### app/page.tsx
```tsx
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { ActiveCrews } from '@/components/dashboard/active-crews'
import { LivePreview } from '@/components/dashboard/live-preview'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import { useDashboardStats } from '@/lib/hooks/use-dashboard-stats'

export default function DashboardPage() {
  const { stats, isLoading } = useDashboardStats()

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Dashboard</h1>
            <p className="text-slate-600 mt-2">
              Monitor and manage your AI agent crews
            </p>
          </div>
          <QuickActions />
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCards stats={stats} isLoading={isLoading} />
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Active Crews - Takes 2 columns */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <ActiveCrews />
          </motion.div>

          {/* Activity Feed - Takes 1 column */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <ActivityFeed />
          </motion.div>
        </div>

        {/* Live Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <LivePreview />
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
```

## Components

### components/layouts/dashboard-layout.tsx
```tsx
'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Sidebar } from '@/components/navigation/sidebar'
import { Header } from '@/components/navigation/header'
import { MobileMenu } from '@/components/navigation/mobile-menu'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Mobile Menu */}
      <MobileMenu
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />

      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col">
        <Sidebar />
      </div>

      {/* Main Content */}
      <div className="lg:pl-80">
        <Header onMenuClick={() => setSidebarOpen(true)} />

        <main className="py-8">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              {children}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  )
}
```

### components/navigation/sidebar.tsx
```tsx
'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  HomeIcon,
  CpuChipIcon,
  DocumentDuplicateIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  PlusIcon,
  BoltIcon
} from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

const navigation = [
  { name: 'Dashboard', href: '/', icon: HomeIcon },
  { name: 'My Crews', href: '/crews', icon: CpuChipIcon },
  { name: 'Templates', href: '/templates', icon: DocumentDuplicateIcon },
  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },
  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-slate-900 px-6 pb-4">
      {/* Logo */}
      <div className="flex h-20 shrink-0 items-center">
        <Link href="/" className="flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600">
            <BoltIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <span className="text-xl font-bold text-white">CrewCraft</span>
            <span className="block text-xs text-slate-400">AI Platform</span>
          </div>
        </Link>
      </div>

      {/* Quick Create Button */}
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Link
          href="/crews/new"
          className="group flex w-full items-center justify-center gap-2 rounded-lg bg-primary-600 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 transition-colors"
        >
          <PlusIcon className="h-5 w-5" />
          Create New Crew
        </Link>
      </motion.div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <div className="text-xs font-semibold leading-6 text-slate-400 uppercase tracking-wider">
              Navigation
            </div>
            <ul role="list" className="-mx-2 mt-4 space-y-1">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={clsx(
                        isActive
                          ? 'bg-slate-800 text-white'
                          : 'text-slate-400 hover:text-white hover:bg-slate-800',
                        'group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium transition-colors'
                      )}
                    >
                      <item.icon
                        className={clsx(
                          isActive ? 'text-white' : 'text-slate-400 group-hover:text-white',
                          'h-5 w-5 shrink-0'
                        )}
                        aria-hidden="true"
                      />
                      {item.name}
                      {isActive && (
                        <motion.div
                          layoutId="activeIndicator"
                          className="ml-auto h-2 w-2 rounded-full bg-primary-500"
                          transition={{ type: "spring", duration: 0.3 }}
                        />
                      )}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </li>

          {/* Bottom Section */}
          <li className="mt-auto">
            <div className="rounded-lg bg-slate-800 p-4">
              <div className="flex items-center gap-3">
                <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                  <span className="text-sm font-semibold text-white">U</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-white">User</p>
                  <p className="text-xs text-slate-400">Pro Plan</p>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </nav>
    </div>
  )
}
```

### components/navigation/header.tsx
```tsx
'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Bars3Icon,
  BellIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { Fragment } from 'react'

interface HeaderProps {
  onMenuClick: () => void
}

export function Header({ onMenuClick }: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="sticky top-0 z-40 flex h-20 shrink-0 items-center gap-x-4 border-b border-slate-200 bg-white/80 backdrop-blur-sm px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
      {/* Mobile menu button */}
      <button
        type="button"
        className="-m-2.5 p-2.5 text-slate-700 lg:hidden"
        onClick={onMenuClick}
      >
        <span className="sr-only">Open sidebar</span>
        <Bars3Icon className="h-6 w-6" aria-hidden="true" />
      </button>

      {/* Separator */}
      <div className="h-6 w-px bg-slate-200 lg:hidden" aria-hidden="true" />

      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        {/* Search */}
        <div className="relative flex flex-1 items-center">
          <MagnifyingGlassIcon className="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-slate-400 pl-3" />
          <input
            id="search-field"
            className="block h-full w-full border-0 py-0 pl-10 pr-0 text-slate-900 placeholder:text-slate-400 focus:ring-0 bg-transparent sm:text-sm"
            placeholder="Search crews, templates, or agents..."
            type="search"
            name="search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-x-4 lg:gap-x-6">
          {/* Notifications */}
          <button
            type="button"
            className="relative -m-2.5 p-2.5 text-slate-400 hover:text-slate-500"
          >
            <span className="sr-only">View notifications</span>
            <BellIcon className="h-6 w-6" aria-hidden="true" />
            <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
              3
            </span>
          </button>

          {/* Separator */}
          <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-slate-200" aria-hidden="true" />

          {/* Profile dropdown */}
          <Menu as="div" className="relative">
            <Menu.Button className="-m-1.5 flex items-center p-1.5 hover:bg-slate-50 rounded-lg transition-colors">
              <span className="sr-only">Open user menu</span>
              <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                <span className="text-sm font-semibold text-white">U</span>
              </div>
              <span className="hidden lg:flex lg:items-center">
                <span className="ml-4 text-sm font-semibold leading-6 text-slate-900" aria-hidden="true">
                  User Name
                </span>
                <ChevronDownIcon className="ml-2 h-5 w-5 text-slate-400" aria-hidden="true" />
              </span>
            </Menu.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-slate-900/5 focus:outline-none">
                <Menu.Item>
                  {({ active }) => (
                    <a
                      href="#"
                      className={`block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? 'bg-slate-50' : ''}`}
                    >
                      Your profile
                    </a>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <a
                      href="#"
                      className={`block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? 'bg-slate-50' : ''}`}
                    >
                      Settings
                    </a>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <a
                      href="#"
                      className={`block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? 'bg-slate-50' : ''}`}
                    >
                      Sign out
                    </a>
                  )}
                </Menu.Item>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
      </div>
    </div>
  )
}
```

### components/navigation/mobile-menu.tsx
```tsx
'use client'

import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { Sidebar } from './sidebar'

interface MobileMenuProps {
  open: boolean
  onClose: () => void
}

export function MobileMenu({ open, onClose }: MobileMenuProps) {
  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="transition-opacity ease-linear duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-slate-900/80" />
        </Transition.Child>

        <div className="fixed inset-0 flex">
          <Transition.Child
            as={Fragment}
            enter="transition ease-in-out duration-300 transform"
            enterFrom="-translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="-translate-x-full"
          >
            <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
              <Transition.Child
                as={Fragment}
                enter="ease-in-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in-out duration-300"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button type="button" className="-m-2.5 p-2.5" onClick={onClose}>
                    <span className="sr-only">Close sidebar</span>
                    <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </Transition.Child>
              <Sidebar />
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
```

### components/dashboard/stats-cards.tsx
```tsx
'use client'

import { motion } from 'framer-motion'
import {
  CpuChipIcon,
  CheckCircleIcon,
  ClockIcon,
  BoltIcon
} from '@heroicons/react/24/outline'
import { DashboardStats } from '@/types/dashboard'

interface StatsCardsProps {
  stats: DashboardStats | null
  isLoading: boolean
}

export function StatsCards({ stats, isLoading }: StatsCardsProps) {
  const cards = [
    {
      name: 'Active Crews',
      value: stats?.activeCrews || 0,
      icon: CpuChipIcon,
      color: 'emerald',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      name: 'Total Tasks',
      value: stats?.totalTasks || 0,
      icon: CheckCircleIcon,
      color: 'blue',
      change: '+19%',
      changeType: 'positive' as const,
    },
    {
      name: 'Success Rate',
      value: `${stats?.successRate || 0}%`,
      icon: CheckCircleIcon,
      color: 'green',
      change: '+2.1%',
      changeType: 'positive' as const,
    },
    {
      name: 'Avg Speed',
      value: `${stats?.averageSpeed || 0}s`,
      icon: BoltIcon,
      color: 'purple',
      change: '-0.3s',
      changeType: 'positive' as const,
    },
  ]

  const colorClasses = {
    emerald: 'text-emerald-600 bg-emerald-100',
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    purple: 'text-purple-600 bg-purple-100',
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-xl border border-slate-200 p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-slate-200 rounded w-20 mb-2"></div>
              <div className="h-8 bg-slate-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-slate-200 rounded w-12"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => (
        <motion.div
          key={card.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-xl border border-slate-200 p-6 hover:shadow-lg transition-shadow duration-200"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-slate-600">{card.name}</p>
              <p className="text-3xl font-bold text-slate-900 mt-2">{card.value}</p>
              <div className="flex items-center mt-2">
                <span className={`text-sm font-medium ${
                  card.changeType === 'positive' ? 'text-emerald-600' : 'text-red-600'
                }`}>
                  {card.change}
                </span>
                <span className="text-sm text-slate-500 ml-1">vs last month</span>
              </div>
            </div>
            <div className={`p-3 rounded-lg ${colorClasses[card.color]}`}>
              <card.icon className="h-6 w-6" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}
```

### components/dashboard/quick-actions.tsx
```tsx
'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  PlusIcon,
  DocumentDuplicateIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

const actions = [
  {
    name: 'New Crew',
    href: '/crews/new',
    icon: PlusIcon,
    color: 'primary',
  },
  {
    name: 'Templates',
    href: '/templates',
    icon: DocumentDuplicateIcon,
    color: 'slate',
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: ChartBarIcon,
    color: 'slate',
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Cog6ToothIcon,
    color: 'slate',
  },
]

export function QuickActions() {
  return (
    <div className="flex items-center gap-3">
      {actions.map((action, index) => (
        <motion.div
          key={action.name}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Link
            href={action.href}
            className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg border transition-colors ${
              action.color === 'primary'
                ? 'bg-primary-600 text-white border-primary-600 hover:bg-primary-700'
                : 'bg-white text-slate-700 border-slate-200 hover:bg-slate-50'
            }`}
          >
            <action.icon className="h-4 w-4" />
            <span className="hidden sm:inline">{action.name}</span>
          </Link>
        </motion.div>
      ))}
    </div>
  )
}
```

### components/dashboard/active-crews.tsx
```tsx
'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import {
  PlusIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  EllipsisVerticalIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline'
import { Menu } from '@headlessui/react'
import { useActiveCrews } from '@/lib/hooks/use-active-crews'
import { CrewStatus } from '@/types/crew'
import { clsx } from 'clsx'

export function ActiveCrews() {
  const { crews, isLoading } = useActiveCrews()

  const getStatusColor = (status: CrewStatus) => {
    switch (status) {
      case 'running':
        return 'bg-emerald-500'
      case 'pending':
        return 'bg-amber-500'
      case 'completed':
        return 'bg-slate-400'
      case 'failed':
        return 'bg-red-500'
      default:
        return 'bg-slate-400'
    }
  }

  const getStatusText = (status: CrewStatus) => {
    return status.charAt(0).toUpperCase() + status.slice(1)
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl border border-slate-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-slate-900">Active Crews</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-slate-200 rounded-lg h-40"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl border border-slate-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-slate-900">Active Crews</h3>
        <Link
          href="/crews/new"
          className="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          New Crew
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {crews.map((crew, index) => (
          <motion.div
            key={crew.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className="group relative bg-slate-50 rounded-lg p-4 hover:bg-slate-100 transition-colors cursor-pointer"
          >
            {/* Status Indicator */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className={clsx(
                  'h-3 w-3 rounded-full',
                  getStatusColor(crew.status),
                  crew.status === 'running' && 'animate-pulse'
                )}>
                </div>
                <span className="text-sm font-medium text-slate-600">
                  {getStatusText(crew.status)}
                </span>
              </div>

              <Menu as="div" className="relative">
                <Menu.Button className="p-1 rounded-md hover:bg-slate-200 transition-colors">
                  <EllipsisVerticalIcon className="h-4 w-4 text-slate-400" />
                </Menu.Button>
                <Menu.Items className="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <Menu.Item>
                    <button className="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                      View Details
                    </button>
                  </Menu.Item>
                  <Menu.Item>
                    <button className="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">
                      Edit
                    </button>
                  </Menu.Item>
                  <Menu.Item>
                    <button className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-slate-50">
                      Delete
                    </button>
                  </Menu.Item>
                </Menu.Items>
              </Menu>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-slate-200 rounded-full h-1 mb-4">
              <div
                className={clsx(
                  'h-1 rounded-full transition-all duration-300',
                  crew.status === 'running' ? 'bg-primary-600' :
                  crew.status === 'completed' ? 'bg-emerald-500' :
                  crew.status === 'failed' ? 'bg-red-500' : 'bg-amber-500'
                )}
                style={{ width: `${crew.progress}%` }}
              />
            </div>

            {/* Crew Info */}
            <div className="mb-4">
              <h4 className="text-base font-semibold text-slate-900 mb-2">
                {crew.name}
              </h4>
              <p className="text-sm text-slate-600 mb-2">
                {crew.agents?.length || 0} agents • {crew.tasks?.length || 0} tasks
              </p>
              {crew.model && (
                <span className="inline-flex items-center px-2 py-1 rounded-md bg-blue-100 text-blue-800 text-xs font-medium">
                  {crew.model}
                </span>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              {crew.status === 'running' ? (
                <button className="p-2 rounded-md bg-amber-100 text-amber-600 hover:bg-amber-200 transition-colors">
                  <PauseIcon className="h-4 w-4" />
                </button>
              ) : (
                <button className="p-2 rounded-md bg-emerald-100 text-emerald-600 hover:bg-emerald-200 transition-colors">
                  <PlayIcon className="h-4 w-4" />
                </button>
              )}
              <button className="p-2 rounded-md bg-red-100 text-red-600 hover:bg-red-200 transition-colors">
                <StopIcon className="h-4 w-4" />
              </button>

              <Link
                href={`/crews/${crew.id}`}
                className="ml-auto text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                View Details
              </Link>
            </div>

            {/* Hover Effect */}
            <div className="absolute inset-0 rounded-lg border-2 border-transparent group-hover:border-primary-200 transition-colors pointer-events-none" />
          </motion.div>
        ))}
      </div>

      {crews.length === 0 && (
        <div className="text-center py-12">
          <CpuChipIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-slate-900 mb-2">No active crews</h4>
          <p className="text-slate-600 mb-4">Get started by creating your first AI crew</p>
          <Link
            href="/crews/new"
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            Create Your First Crew
          </Link>
        </div>
      )}
    </div>
  )
}
```

### app/globals.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap');

@layer base {
  :root {
    --font-inter: 'Inter', system-ui, sans-serif;
    --font-jetbrains-mono: 'JetBrains Mono', 'Menlo', monospace;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  * {
    border-color: theme('colors.slate.200');
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .gradient-success {
    @apply bg-gradient-to-r from-emerald-500 to-emerald-600;
  }

  .gradient-warning {
    @apply bg-gradient-to-r from-amber-500 to-amber-600;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px -10px theme('colors.primary.500');
    }
    to {
      box-shadow: 0 0 20px -5px theme('colors.primary.500'), 0 0 40px -10px theme('colors.primary.300');
    }
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .code-highlight {
    @apply bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-slate-100 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-slate-300 rounded-full hover:bg-slate-400;
  }

  /* Loading animations */
  .loading-dots {
    display: inline-block;
  }

  .loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
  }

  @keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }

  /* Status indicators */
  .status-running {
    @apply relative;
  }

  .status-running::before {
    content: '';
    @apply absolute -inset-1 bg-emerald-400 rounded-full animate-ping;
  }

  .status-pending {
    @apply relative;
  }

  .status-pending::before {
    content: '';
    @apply absolute -inset-1 bg-amber-400 rounded-full animate-pulse;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent;
  }

  .shadow-glow {
    box-shadow: 0 0 30px -5px theme('colors.primary.500/50');
  }

  .border-gradient {
    @apply border-2 border-transparent bg-gradient-to-r from-primary-500 to-primary-600 bg-origin-border;
  }
}
```

### app/page.tsx
```tsx
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { ActiveCrews } from '@/components/dashboard/active-crews'
import { LivePreview } from '@/components/dashboard/live-preview'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import { useDashboardStats } from '@/lib/hooks/use-dashboard-stats'

export default function DashboardPage() {
  const { stats, isLoading } = useDashboardStats()

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Dashboard</h1>
            <p className="text-slate-600 mt-2">
              Monitor and manage your AI agent crews
            </p>
          </div>
          <QuickActions />
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCards stats={stats} isLoading={isLoading} />
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Active Crews - Takes 2 columns */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <ActiveCrews />
          </motion.div>

          {/* Activity Feed - Takes 1 column */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <ActivityFeed />
          </motion.div>
        </div>

        {/* Live Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <LivePreview />
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
```
