# CrewCraft AI Platform - Complete Codebase Extraction

This document contains all the code files extracted from the repository in a GitHub-like format.

## Project Structure

```
crewcraft-ai-platform/
├── package.json
├── tailwind.config.js
├── postcss.config.js
├── next.config.js
├── tsconfig.json
├── .env.local.example
├── app/
│   ├── layout.tsx
│   ├── globals.css
│   └── page.tsx
├── components/
│   ├── layouts/
│   │   └── dashboard-layout.tsx
│   ├── navigation/
│   │   ├── sidebar.tsx
│   │   ├── header.tsx
│   │   └── mobile-menu.tsx
│   ├── dashboard/
│   │   ├── stats-cards.tsx
│   │   ├── active-crews.tsx
│   │   ├── live-preview.tsx
│   │   ├── quick-actions.tsx
│   │   └── activity-feed.tsx
│   ├── crews/
│   │   ├── crew-form.tsx
│   │   ├── crew-card.tsx
│   │   ├── crew-details.tsx
│   │   └── agent-builder.tsx
│   ├── templates/
│   │   ├── template-gallery.tsx
│   │   ├── template-card.tsx
│   │   └── template-preview.tsx
│   ├── ui/
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── modal.tsx
│   │   ├── loading.tsx
│   │   └── toast.tsx
│   └── providers.tsx
├── lib/
│   ├── supabase.ts
│   ├── cerebras.ts
│   ├── utils.ts
│   ├── validations.ts
│   └── hooks/
│       ├── use-dashboard-stats.ts
│       ├── use-active-crews.ts
│       ├── use-live-preview.ts
│       └── use-crews.ts
├── types/
│   ├── dashboard.ts
│   ├── crew.ts
│   ├── agent.ts
│   └── template.ts
└── preview/
    └── wireframe.svg
```

## Configuration Files

### package.json
```json
{
  "name": "crewcraft-ai-platform",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "next": "14.0.4",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.3.3",
    "@types/node": "^20.10.5",
    "@types/react": "^18.2.45",
    "@types/react-dom": "^18.2.18",
    "tailwindcss": "^3.4.0",
    "postcss": "^8.4.32",
    "autoprefixer": "^10.4.16",
    "@headlessui/react": "^1.7.17",
    "@heroicons/react": "^2.0.18",
    "framer-motion": "^10.16.16",
    "lucide-react": "^0.303.0",
    "recharts": "^2.8.0",
    "@supabase/supabase-js": "^2.38.5",
    "axios": "^1.6.2",
    "react-hot-toast": "^2.4.1",
    "date-fns": "^3.0.6",
    "uuid": "^9.0.1",
    "@types/uuid": "^9.0.7",
    "react-syntax-highlighter": "^15.5.0",
    "@types/react-syntax-highlighter": "^15.5.11",
    "react-hook-form": "^7.48.2",
    "@hookform/resolvers": "^3.3.2",
    "zod": "^3.22.4",
    "clsx": "^2.0.0",
    "class-variance-authority": "^0.7.0"
  },
  "devDependencies": {
    "eslint": "^8.56.0",
    "eslint-config-next": "14.0.4",
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10"
  }
}
```

### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          900: '#312e81',
        },
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        emerald: {
          500: '#10b981',
          600: '#059669',
        },
        amber: {
          500: '#f59e0b',
          600: '#d97706',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Menlo', 'monospace'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-subtle': 'bounce 2s infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

### postcss.config.js
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['images.unsplash.com', 'avatars.githubusercontent.com'],
  },
  async rewrites() {
    return [
      {
        source: '/api/crews/:path*',
        destination: '/api/crews/:path*',
      },
    ]
  },
}

module.exports = nextConfig
```

### tsconfig.json
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*"],
      "@/lib/*": ["./lib/*"],
      "@/types/*": ["./types/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### .env.local.example
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Cerebras API Configuration
CEREBRAS_API_KEY=csk-your_cerebras_api_key

# Application Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Optional: Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_vercel_analytics_id
```

## App Directory

### app/layout.tsx
```tsx
import type { Metadata } from 'next'
import { Inter, JetBrains_Mono } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
})

export const metadata: Metadata = {
  title: 'CrewCraft AI Platform - Multi-Agent AI Orchestration',
  description: 'Create, manage, and deploy AI agent crews with Cerebras ultra-fast inference. Build sophisticated multi-agent workflows for complex tasks.',
  keywords: 'AI, CrewAI, Cerebras, Multi-Agent, Artificial Intelligence, Automation, Workflows',
  authors: [{ name: 'CrewCraft Team' }],
  openGraph: {
    title: 'CrewCraft AI Platform',
    description: 'Enterprise-grade multi-agent AI orchestration platform',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${jetbrainsMono.variable}`}>
      <body className="font-sans antialiased bg-slate-50 text-slate-800">
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              className: 'bg-white shadow-lg border border-slate-200',
            }}
          />
        </Providers>
      </body>
    </html>
  )
}
```

### app/globals.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap');

@layer base {
  :root {
    --font-inter: 'Inter', system-ui, sans-serif;
    --font-jetbrains-mono: 'JetBrains Mono', 'Menlo', monospace;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  * {
    border-color: theme('colors.slate.200');
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .gradient-success {
    @apply bg-gradient-to-r from-emerald-500 to-emerald-600;
  }

  .gradient-warning {
    @apply bg-gradient-to-r from-amber-500 to-amber-600;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px -10px theme('colors.primary.500');
    }
    to {
      box-shadow: 0 0 20px -5px theme('colors.primary.500'), 0 0 40px -10px theme('colors.primary.300');
    }
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .code-highlight {
    @apply bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-slate-100 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-slate-300 rounded-full hover:bg-slate-400;
  }

  /* Loading animations */
  .loading-dots {
    display: inline-block;
  }

  .loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
  }

  @keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }

  /* Status indicators */
  .status-running {
    @apply relative;
  }

  .status-running::before {
    content: '';
    @apply absolute -inset-1 bg-emerald-400 rounded-full animate-ping;
  }

  .status-pending {
    @apply relative;
  }

  .status-pending::before {
    content: '';
    @apply absolute -inset-1 bg-amber-400 rounded-full animate-pulse;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent;
  }

  .shadow-glow {
    box-shadow: 0 0 30px -5px theme('colors.primary.500/50');
  }

  .border-gradient {
    @apply border-2 border-transparent bg-gradient-to-r from-primary-500 to-primary-600 bg-origin-border;
  }
}
```

### app/page.tsx
```tsx
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { ActiveCrews } from '@/components/dashboard/active-crews'
import { LivePreview } from '@/components/dashboard/live-preview'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import { useDashboardStats } from '@/lib/hooks/use-dashboard-stats'

export default function DashboardPage() {
  const { stats, isLoading } = useDashboardStats()

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Dashboard</h1>
            <p className="text-slate-600 mt-2">
              Monitor and manage your AI agent crews
            </p>
          </div>
          <QuickActions />
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCards stats={stats} isLoading={isLoading} />
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Active Crews - Takes 2 columns */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <ActiveCrews />
          </motion.div>

          {/* Activity Feed - Takes 1 column */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <ActivityFeed />
          </motion.div>
        </div>

        {/* Live Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <LivePreview />
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
```
