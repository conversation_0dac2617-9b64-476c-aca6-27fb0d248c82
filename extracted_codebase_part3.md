# CrewCraft AI Platform - Complete Codebase Extraction (Part 3)

This is the continuation of the codebase extraction with hooks and remaining components.

## Hooks

### lib/hooks/use-dashboard-stats.ts
```typescript
'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { DashboardStats } from '@/types/dashboard'

export function useDashboardStats() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchStats() {
      try {
        setIsLoading(true)
        
        // Fetch active crews count
        const { count: activeCrews } = await supabase
          .from('crews')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'running')

        // Fetch total tasks (sum of all tasks in all crews)
        const { data: crews } = await supabase
          .from('crews')
          .select('tasks')
        
        const totalTasks = crews?.reduce((sum, crew) => sum + (crew.tasks?.length || 0), 0) || 0

        // Calculate success rate (completed crews / total crews)
        const { count: totalCrews } = await supabase
          .from('crews')
          .select('*', { count: 'exact', head: true })

        const { count: completedCrews } = await supabase
          .from('crews')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'completed')

        const successRate = totalCrews ? Math.round((completedCrews || 0) / totalCrews * 100) : 0

        // Mock average speed for now (would be calculated from execution logs)
        const averageSpeed = 2.3

        setStats({
          activeCrews: activeCrews || 0,
          totalTasks,
          successRate,
          averageSpeed,
        })
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch stats')
      } finally {
        setIsLoading(false)
      }
    }

    fetchStats()
  }, [])

  return { stats, isLoading, error }
}
```

### lib/hooks/use-active-crews.ts
```typescript
'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Crew } from '@/types/crew'

export function useActiveCrews() {
  const [crews, setCrews] = useState<Crew[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchActiveCrews() {
      try {
        setIsLoading(true)
        
        const { data, error } = await supabase
          .from('crews')
          .select('*')
          .in('status', ['running', 'pending'])
          .order('updated_at', { ascending: false })
          .limit(10)

        if (error) throw error

        const formattedCrews: Crew[] = data?.map(crew => ({
          id: crew.id,
          name: crew.name,
          description: crew.description,
          status: crew.status as Crew['status'],
          progress: crew.progress,
          agents: crew.agents || [],
          tasks: crew.tasks || [],
          model: crew.model,
          createdAt: crew.created_at,
          updatedAt: crew.updated_at,
        })) || []

        setCrews(formattedCrews)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch crews')
      } finally {
        setIsLoading(false)
      }
    }

    fetchActiveCrews()

    // Set up real-time subscription
    const subscription = supabase
      .channel('crews_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'crews' },
        () => {
          fetchActiveCrews()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  return { crews, isLoading, error }
}
```

### lib/hooks/use-live-preview.ts
```typescript
'use client'

import { useState, useEffect, useRef } from 'react'

export interface LogEntry {
  timestamp: string
  agent: string
  level: 'info' | 'success' | 'warning' | 'error'
  message: string
}

export function useLivePreview() {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentCrew, setCurrentCrew] = useState<string | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const addLog = (entry: Omit<LogEntry, 'timestamp'>) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, { ...entry, timestamp }])
  }

  const startPreview = () => {
    setIsRunning(true)
    setCurrentCrew('Research Crew')
    setLogs([])

    // Simulate live logs
    const mockLogs = [
      { agent: 'Researcher', level: 'info' as const, message: 'Starting market analysis task...' },
      { agent: 'Researcher', level: 'info' as const, message: 'Gathering data from multiple sources' },
      { agent: 'Researcher', level: 'success' as const, message: 'Found 47 relevant market reports' },
      { agent: 'Writer', level: 'info' as const, message: 'Processing research findings...' },
      { agent: 'Writer', level: 'info' as const, message: 'Generating executive summary' },
      { agent: 'Reviewer', level: 'info' as const, message: 'Validating content quality...' },
      { agent: 'Reviewer', level: 'success' as const, message: 'Content approved for delivery' },
    ]

    let logIndex = 0
    intervalRef.current = setInterval(() => {
      if (logIndex < mockLogs.length) {
        addLog(mockLogs[logIndex])
        logIndex++
      } else {
        // Add some dynamic logs
        const agents = ['Researcher', 'Writer', 'Reviewer']
        const messages = [
          'Processing new data batch...',
          'Analyzing trends and patterns',
          'Generating insights report',
          'Validating output quality',
          'Optimizing content structure',
        ]
        const randomAgent = agents[Math.floor(Math.random() * agents.length)]
        const randomMessage = messages[Math.floor(Math.random() * messages.length)]
        addLog({ agent: randomAgent, level: 'info', message: randomMessage })
      }
    }, 2000)
  }

  const stopPreview = () => {
    setIsRunning(false)
    setCurrentCrew(null)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    addLog({ agent: 'System', level: 'info', message: 'Preview session ended' })
  }

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    logs,
    isRunning,
    currentCrew,
    startPreview,
    stopPreview,
  }
}
```

### lib/hooks/use-activity-feed.ts
```typescript
'use client'

import { useState, useEffect } from 'react'
import { ActivityItem } from '@/types/dashboard'

export function useActivityFeed() {
  const [activities, setActivities] = useState<ActivityItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock activity data
    const mockActivities: ActivityItem[] = [
      {
        id: '1',
        type: 'success',
        title: 'Research Crew completed',
        description: 'Market analysis task finished successfully',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      },
      {
        id: '2',
        type: 'info',
        title: 'New crew created',
        description: 'Content Pipeline crew was created from template',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      },
      {
        id: '3',
        type: 'warning',
        title: 'High token usage',
        description: 'Data Analysis crew exceeded 80% of token limit',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      },
      {
        id: '4',
        type: 'success',
        title: 'Template published',
        description: 'Customer Support template is now available',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      },
      {
        id: '5',
        type: 'error',
        title: 'Crew execution failed',
        description: 'Email Campaign crew encountered an error',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      },
    ]

    setTimeout(() => {
      setActivities(mockActivities)
      setIsLoading(false)
    }, 1000)
  }, [])

  return { activities, isLoading }
}
```

## Utility Functions

### lib/utils.ts
```typescript
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatTime(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function formatDuration(seconds: number) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`
  } else {
    return `${remainingSeconds}s`
  }
}

export function truncateText(text: string, maxLength: number) {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export function generateId() {
  return Math.random().toString(36).substr(2, 9)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
```

### lib/validations.ts
```typescript
import { z } from 'zod'

export const agentSchema = z.object({
  name: z.string().min(1, 'Agent name is required'),
  role: z.string().min(1, 'Agent role is required'),
  goal: z.string().min(1, 'Agent goal is required'),
  backstory: z.string().min(1, 'Agent backstory is required'),
  tools: z.array(z.string()).min(1, 'At least one tool is required'),
  model: z.string().min(1, 'Model selection is required'),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(4000).optional(),
  verbose: z.boolean().optional(),
})

export const taskSchema = z.object({
  description: z.string().min(1, 'Task description is required'),
  expectedOutput: z.string().min(1, 'Expected output is required'),
  agent: z.string().optional(),
  tools: z.array(z.string()).optional(),
  context: z.array(z.string()).optional(),
})

export const crewSchema = z.object({
  name: z.string().min(1, 'Crew name is required'),
  description: z.string().min(1, 'Crew description is required'),
  agents: z.array(agentSchema).min(1, 'At least one agent is required'),
  tasks: z.array(taskSchema).min(1, 'At least one task is required'),
  model: z.string().optional(),
})

export type AgentFormData = z.infer<typeof agentSchema>
export type TaskFormData = z.infer<typeof taskSchema>
export type CrewFormData = z.infer<typeof crewSchema>
```
