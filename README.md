# CrewCraft AI Platform

A comprehensive, enterprise-grade platform for managing AI agent crews with Cerebras integration.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account
- Cerebras API key

### Installation

1. **Clone and install dependencies:**
```bash
npm install
```

2. **Environment Setup:**
```bash
cp .env.local.example .env.local
```

Fill in your environment variables:
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anon key
- `CEREBRAS_API_KEY`: Your Cerebras API key

3. **Run the development server:**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📁 Project Structure

```
crewcraft-ai-platform/
├── app/                          # Next.js 14 App Router
│   ├── layout.tsx               ✅ Root layout
│   ├── globals.css              ✅ Global styles
│   └── page.tsx                 ✅ Dashboard page
├── components/                   # React components
│   ├── layouts/
│   │   └── dashboard-layout.tsx ✅ Main layout
│   ├── navigation/
│   │   ├── sidebar.tsx          ✅ Navigation sidebar
│   │   ├── header.tsx           🔄 Need to extract
│   │   └── mobile-menu.tsx      🔄 Need to extract
│   ├── dashboard/               🔄 Need to extract
│   ├── crews/                   🔄 Need to extract
│   ├── templates/               🔄 Need to extract
│   └── ui/                      🔄 Need to extract
├── lib/                         # Utilities and integrations
│   ├── supabase.ts              ✅ Database client
│   ├── cerebras.ts              🔄 Need to extract
│   ├── utils.ts                 🔄 Need to extract
│   └── hooks/                   🔄 Need to extract
├── types/                       # TypeScript definitions
│   ├── crew.ts                  ✅ Crew types
│   ├── dashboard.ts             🔄 Need to extract
│   └── ...                      🔄 Need to extract
├── package.json                 ✅ Dependencies
├── tailwind.config.js           ✅ Tailwind configuration
├── tsconfig.json                ✅ TypeScript configuration
└── next.config.js               ✅ Next.js configuration
```

## ✅ Successfully Extracted Files

All core files have been successfully extracted and the application is now running! Here's what was completed:

### ✅ Configuration Files
- `package.json` - Dependencies and scripts
- `tailwind.config.js` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration
- `next.config.js` - Next.js configuration
- `postcss.config.js` - PostCSS configuration
- `.env.local.example` - Environment variables template

### ✅ App Directory
- `app/layout.tsx` - Root layout with providers
- `app/globals.css` - Global styles and custom CSS
- `app/page.tsx` - Main dashboard page

### ✅ Navigation Components
- `components/navigation/sidebar.tsx` - Main navigation sidebar
- `components/navigation/header.tsx` - Top header with search
- `components/navigation/mobile-menu.tsx` - Mobile responsive menu

### ✅ Dashboard Components
- `components/dashboard/stats-cards.tsx` - Statistics cards
- `components/dashboard/active-crews.tsx` - Active crews management
- `components/dashboard/live-preview.tsx` - Live execution preview
- `components/dashboard/quick-actions.tsx` - Quick action buttons
- `components/dashboard/activity-feed.tsx` - Activity feed component

### ✅ Layout Components
- `components/layouts/dashboard-layout.tsx` - Main dashboard layout
- `components/providers.tsx` - React context providers

### ✅ Library Files
- `lib/supabase.ts` - Database client and types
- `lib/cerebras.ts` - Cerebras AI API integration
- `lib/utils.ts` - Utility functions
- `lib/hooks/use-dashboard-stats.ts` - Dashboard statistics hook
- `lib/hooks/use-active-crews.ts` - Active crews management hook
- `lib/hooks/use-live-preview.ts` - Live preview functionality hook
- `lib/hooks/use-activity-feed.ts` - Activity feed data hook

### ✅ Type Definitions
- `types/crew.ts` - Crew and agent type definitions
- `types/dashboard.ts` - Dashboard statistics types
- `types/activity.ts` - Activity feed types
- `types/preview.ts` - Live preview types

### 🔄 Additional Components Available in 1.md
The following components are available in the original `1.md` file but not yet extracted:
- Crew management components (create, edit, details)
- Template gallery and management
- UI components (buttons, inputs, modals)
- Additional pages and features

## 📝 Manual Extraction Instructions

To extract the remaining files from `1.md`:

1. **Find the file section** in `1.md` (search for `### /path/to/file.tsx`)
2. **Copy the code** between the code block markers (```tsx ... ```)
3. **Create the file** in the correct directory
4. **Paste the code** into the new file

Example for extracting `components/navigation/header.tsx`:
1. Search for `### /components/navigation/header.tsx` in `1.md`
2. Copy everything between the ```tsx and ``` markers
3. Create `components/navigation/header.tsx`
4. Paste the code

## 🛠 Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Headless UI, Heroicons, Framer Motion
- **Database**: Supabase with real-time subscriptions
- **AI Integration**: Cerebras API for ultra-fast inference
- **Form Handling**: React Hook Form with Zod validation
- **Code Highlighting**: React Syntax Highlighter
- **Charts**: Recharts for analytics

## 🎨 Design System

### Colors
- **Primary**: Indigo (#6366f1) - Modern AI purple
- **Secondary**: Slate (#0f172a) - Professional dark
- **Accent**: Emerald (#10b981) - Success green
- **Warning**: Amber (#f59e0b) - Alert orange

### Typography
- **UI Font**: Inter - Clean, modern sans-serif
- **Code Font**: JetBrains Mono - Developer-friendly monospace

## 🚀 Features

- ✅ **Dashboard**: Real-time statistics and monitoring
- ✅ **Crew Management**: Create and manage AI agent crews
- ✅ **Live Preview**: Real-time execution monitoring
- ✅ **Templates**: Pre-built crew templates
- ✅ **Analytics**: Performance metrics and insights
- ✅ **Responsive Design**: Mobile-first approach

## 📄 License

This project is private and proprietary.

## 🤝 Contributing

Please follow the established patterns and conventions when adding new features.
