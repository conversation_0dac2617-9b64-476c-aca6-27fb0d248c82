# CrewCraft AI Platform - Complete Codebase Extraction (Part 2)

This is the continuation of the codebase extraction.

## Dashboard Components (Continued)

### components/dashboard/activity-feed.tsx
```tsx
'use client'

import { motion } from 'framer-motion'
import { formatDistanceToNow } from 'date-fns'
import { 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { useActivityFeed } from '@/lib/hooks/use-activity-feed'

export function ActivityFeed() {
  const { activities, isLoading } = useActivityFeed()

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success':
        return CheckCircleIcon
      case 'warning':
        return ExclamationTriangleIcon
      case 'error':
        return XCircleIcon
      default:
        return InformationCircleIcon
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'text-emerald-600 bg-emerald-100'
      case 'warning':
        return 'text-amber-600 bg-amber-100'
      case 'error':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-blue-600 bg-blue-100'
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl border border-slate-200 p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-6">Activity Feed</h3>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse flex items-start gap-3">
              <div className="h-8 w-8 bg-slate-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-slate-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl border border-slate-200 p-6">
      <h3 className="text-lg font-semibold text-slate-900 mb-6">Activity Feed</h3>
      
      <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar">
        {activities.map((activity, index) => {
          const Icon = getActivityIcon(activity.type)
          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors"
            >
              <div className={`p-2 rounded-full ${getActivityColor(activity.type)}`}>
                <Icon className="h-4 w-4" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-slate-900 mb-1">
                  {activity.title}
                </p>
                <p className="text-sm text-slate-600 mb-2">
                  {activity.description}
                </p>
                <p className="text-xs text-slate-400">
                  {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                </p>
              </div>
            </motion.div>
          )
        })}
      </div>

      {activities.length === 0 && (
        <div className="text-center py-8">
          <InformationCircleIcon className="h-8 w-8 text-slate-400 mx-auto mb-2" />
          <p className="text-sm text-slate-500">No recent activity</p>
        </div>
      )}
    </div>
  )
}
```

### components/dashboard/live-preview.tsx
```tsx
'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  PlayIcon, 
  PauseIcon, 
  StopIcon,
  ChevronUpIcon,
  ChevronDownIcon 
} from '@heroicons/react/24/outline'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { oneLight } from 'react-syntax-highlighter/dist/cjs/styles/prism'
import { useLivePreview } from '@/lib/hooks/use-live-preview'

export function LivePreview() {
  const [isExpanded, setIsExpanded] = useState(true)
  const { logs, isRunning, currentCrew, startPreview, stopPreview } = useLivePreview()

  const formatLogMessage = (message: string) => {
    // Check if message contains code or structured data
    try {
      const parsed = JSON.parse(message)
      return (
        <SyntaxHighlighter
          language="json"
          style={oneLight}
          customStyle={{
            background: 'transparent',
            padding: '0',
            margin: '0',
            fontSize: '12px',
          }}
        >
          {JSON.stringify(parsed, null, 2)}
        </SyntaxHighlighter>
      )
    } catch {
      // Check if it's a Python code snippet
      if (message.includes('def ') || message.includes('import ') || message.includes('class ')) {
        return (
          <SyntaxHighlighter
            language="python"
            style={oneLight}
            customStyle={{
              background: 'transparent',
              padding: '0',
              margin: '0',
              fontSize: '12px',
            }}
          >
            {message}
          </SyntaxHighlighter>
        )
      }
      return <span className="text-slate-600">{message}</span>
    }
  }

  return (
    <div className="bg-white rounded-xl border border-slate-200 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-200 bg-slate-50">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-semibold text-slate-900">Live Preview</h3>
          {currentCrew && (
            <span className="inline-flex items-center px-2 py-1 rounded-md bg-blue-100 text-blue-800 text-xs font-medium">
              {currentCrew}
            </span>
          )}
          {isRunning && (
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
              className="h-2 w-2 bg-emerald-500 rounded-full"
            />
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Control Buttons */}
          <div className="flex items-center gap-1 bg-white rounded-lg border border-slate-200 p-1">
            <button
              onClick={isRunning ? stopPreview : startPreview}
              className={`p-2 rounded-md transition-colors ${
                isRunning 
                  ? 'bg-red-100 text-red-600 hover:bg-red-200' 
                  : 'bg-emerald-100 text-emerald-600 hover:bg-emerald-200'
              }`}
            >
              {isRunning ? (
                <StopIcon className="h-4 w-4" />
              ) : (
                <PlayIcon className="h-4 w-4" />
              )}
            </button>
          </div>

          {/* Expand/Collapse */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 rounded-md hover:bg-slate-100 transition-colors"
          >
            {isExpanded ? (
              <ChevronUpIcon className="h-4 w-4 text-slate-400" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 text-slate-400" />
            )}
          </button>
        </div>
      </div>

      {/* Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-4 bg-slate-50 h-80 overflow-y-auto font-mono text-sm custom-scrollbar">
              {logs.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="h-12 w-12 bg-slate-200 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <PlayIcon className="h-6 w-6 text-slate-400" />
                    </div>
                    <p className="text-slate-500 mb-2">No active preview session</p>
                    <p className="text-xs text-slate-400">Start a crew to see live execution logs</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {logs.map((log, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="flex items-start gap-3 p-2 rounded-md hover:bg-white/50 transition-colors"
                    >
                      <span className="text-xs text-slate-400 font-medium min-w-0 flex-shrink-0">
                        {log.timestamp}
                      </span>
                      <span className={`text-xs font-medium min-w-0 flex-shrink-0 ${
                        log.level === 'error' ? 'text-red-600' :
                        log.level === 'warning' ? 'text-amber-600' :
                        log.level === 'success' ? 'text-emerald-600' :
                        'text-blue-600'
                      }`}>
                        [{log.agent}]
                      </span>
                      <div className="flex-1 min-w-0">
                        {formatLogMessage(log.message)}
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
```

## Type Definitions

### types/dashboard.ts
```typescript
export interface DashboardStats {
  activeCrews: number
  totalTasks: number
  successRate: number
  averageSpeed: number
}

export interface ActivityItem {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  description: string
  timestamp: string
}
```

### types/crew.ts
```typescript
export type CrewStatus = 'running' | 'pending' | 'completed' | 'failed' | 'paused'

export interface Agent {
  id: string
  name: string
  role: string
  goal: string
  backstory: string
  tools: string[]
  model?: string
}

export interface Task {
  id: string
  description: string
  expectedOutput: string
  agent?: string
  tools?: string[]
  context?: string[]
}

export interface Crew {
  id: string
  name: string
  description: string
  status: CrewStatus
  progress: number
  agents: Agent[]
  tasks: Task[]
  model?: string
  createdAt: string
  updatedAt: string
}
```

### types/agent.ts
```typescript
export interface AgentConfig {
  name: string
  role: string
  goal: string
  backstory: string
  tools: string[]
  model: string
  temperature?: number
  maxTokens?: number
  verbose?: boolean
}

export interface AgentTemplate {
  id: string
  name: string
  description: string
  category: string
  config: AgentConfig
  tags: string[]
}
```

### types/template.ts
```typescript
export interface CrewTemplate {
  id: string
  name: string
  description: string
  category: string
  agents: Agent[]
  tasks: Task[]
  tags: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: string
  usageCount: number
  rating: number
  createdAt: string
}

export interface TemplateCategory {
  id: string
  name: string
  description: string
  icon: string
  count: number
}
```

## Library Files

### lib/supabase.ts
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      crews: {
        Row: {
          id: string
          name: string
          description: string
          status: string
          progress: number
          agents: any[]
          tasks: any[]
          model: string | null
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          status?: string
          progress?: number
          agents: any[]
          tasks: any[]
          model?: string | null
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          status?: string
          progress?: number
          agents?: any[]
          tasks?: any[]
          model?: string | null
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      templates: {
        Row: {
          id: string
          name: string
          description: string
          category: string
          agents: any[]
          tasks: any[]
          tags: string[]
          difficulty: string
          estimated_time: string
          usage_count: number
          rating: number
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          category: string
          agents: any[]
          tasks: any[]
          tags?: string[]
          difficulty?: string
          estimated_time?: string
          usage_count?: number
          rating?: number
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          category?: string
          agents?: any[]
          tasks?: any[]
          tags?: string[]
          difficulty?: string
          estimated_time?: string
          usage_count?: number
          rating?: number
          created_at?: string
        }
      }
    }
  }
}
```

### lib/cerebras.ts
```typescript
import axios from 'axios'

const CEREBRAS_API_URL = 'https://api.cerebras.ai/v1'
const CEREBRAS_API_KEY = process.env.CEREBRAS_API_KEY

export interface CerebrasMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface CerebrasCompletionRequest {
  model: string
  messages: CerebrasMessage[]
  temperature?: number
  max_tokens?: number
  stream?: boolean
}

export interface CerebrasCompletionResponse {
  id: string
  object: string
  created: number
  model: string
  choices: {
    index: number
    message: CerebrasMessage
    finish_reason: string
  }[]
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

class CerebrasClient {
  private apiKey: string
  private baseURL: string

  constructor(apiKey: string = CEREBRAS_API_KEY!) {
    this.apiKey = apiKey
    this.baseURL = CEREBRAS_API_URL
  }

  async createCompletion(request: CerebrasCompletionRequest): Promise<CerebrasCompletionResponse> {
    try {
      const response = await axios.post(
        `${this.baseURL}/chat/completions`,
        request,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      )
      return response.data
    } catch (error) {
      console.error('Cerebras API error:', error)
      throw error
    }
  }

  async streamCompletion(
    request: CerebrasCompletionRequest,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      const response = await axios.post(
        `${this.baseURL}/chat/completions`,
        { ...request, stream: true },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          responseType: 'stream',
        }
      )

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n')
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') return
            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices[0]?.delta?.content
              if (content) {
                onChunk(content)
              }
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      })
    } catch (error) {
      console.error('Cerebras streaming error:', error)
      throw error
    }
  }
}

export const cerebras = new CerebrasClient()

// Available Cerebras models
export const CEREBRAS_MODELS = {
  LLAMA_3_1_8B: 'llama3.1-8b',
  LLAMA_3_1_70B: 'llama3.1-70b',
} as const

export type CerebrasModel = typeof CEREBRAS_MODELS[keyof typeof CEREBRAS_MODELS]
```
