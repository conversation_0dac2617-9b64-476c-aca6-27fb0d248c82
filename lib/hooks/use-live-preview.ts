import { useState, useEffect, useCallback } from 'react'
import { LogEntry } from '@/types/preview'

export function useLivePreview() {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentCrew, setCurrentCrew] = useState<string | null>(null)

  const addLog = useCallback((log: LogEntry) => {
    setLogs(prev => [...prev, log].slice(-100)) // Keep only last 100 logs
  }, [])

  const startPreview = useCallback(() => {
    setIsRunning(true)
    setCurrentCrew('Research Crew')
    setLogs([])

    // Simulate live logs
    const mockLogs: LogEntry[] = [
      { 
        timestamp: new Date().toLocaleTimeString(), 
        level: 'info' as const,
        agent: 'Researcher', 
        message: 'Starting market trend analysis...' 
      },
      { 
        timestamp: new Date().toLocaleTimeString(), 
        level: 'info' as const,
        agent: 'Researcher',
        message: 'Searching for latest AI market data...'
      },
      {
        timestamp: new Date().toLocaleTimeString(),
        level: 'success' as const,
        agent: 'Researcher',
        message: 'Found 47 relevant sources for analysis'
      },
      {
        timestamp: new Date().toLocaleTimeString(),
        level: 'info' as const,
        agent: 'Writer',
        message: 'Generating executive summary...'
      },
      {
        timestamp: new Date().toLocaleTimeString(),
        level: 'info' as const,
        agent: 'Writer',
        message: '{"section": "market_overview", "progress": 45, "words": 287}'
      },
      {
        timestamp: new Date().toLocaleTimeString(),
        level: 'info' as const,
        agent: 'Reviewer',
        message: 'Validating content quality and accuracy...'
      },
      {
        timestamp: new Date().toLocaleTimeString(),
        level: 'success' as const,
        agent: 'Reviewer', 
        message: 'Content quality score: 94/100' 
      },
    ]

    let index = 0
    const interval = setInterval(() => {
      if (index < mockLogs.length) {
        addLog(mockLogs[index])
        index++
      } else {
        clearInterval(interval)
        setIsRunning(false)
        addLog({
          timestamp: new Date().toLocaleTimeString(),
          level: 'success',
          agent: 'System',
          message: 'Task completed successfully!'
        })
      }
    }, 2000)

    return () => clearInterval(interval)
  }, [addLog])

  const stopPreview = useCallback(() => {
    setIsRunning(false)
    addLog({
      timestamp: new Date().toLocaleTimeString(),
      level: 'warning',
      agent: 'System',
      message: 'Preview stopped by user'
    })
  }, [addLog])

  return {
    logs,
    isRunning,
    currentCrew,
    startPreview,
    stopPreview,
  }
}
