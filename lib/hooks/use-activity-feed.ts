import { useState, useEffect } from 'react'
import { Activity } from '@/types/activity'
import { supabase, hasSupabaseCredentials } from '@/lib/supabase'

export function useActivityFeed() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchActivities()
  }, [])

  async function fetchActivities() {
    try {
      setIsLoading(true)

      // If no Supabase credentials, use mock data immediately
      if (!hasSupabaseCredentials()) {
        console.log('Using mock activities - Supabase credentials not configured')
        setMockActivities()
        return
      }

      const { data, error } = await supabase
        .from('activities')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(10)

      if (error) throw error

      setActivities(data || [])
    } catch (error) {
      console.error('Error fetching activities:', error)
      setMockActivities()
    } finally {
      setIsLoading(false)
    }
  }

  function setMockActivities() {
      // Mock data for demo
      setActivities([
        {
          id: '1',
          type: 'success',
          title: 'Crew Completed',
          description: 'Research Crew finished market analysis task',
          crewName: 'Research Crew',
          timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
        },
        {
          id: '2',
          type: 'info',
          title: 'New Task Started',
          description: 'Content Pipeline began blog post generation',
          crewName: 'Content Pipeline',
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
        },
        {
          id: '3',
          type: 'warning',
          title: 'Rate Limit Warning',
          description: 'API usage approaching daily limit',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        },
        {
          id: '4',
          type: 'success',
          title: 'Template Created',
          description: 'New workflow template "Data Analysis" created',
          timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
        },
        {
          id: '5',
          type: 'info',
          title: 'Crew Updated',
          description: 'Research Crew configuration updated',
          crewName: 'Research Crew',
          timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
        },
      ])
  }

  return { activities, isLoading, refetch: fetchActivities }
}
