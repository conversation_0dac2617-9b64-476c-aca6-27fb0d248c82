import { useState, useEffect } from 'react'
import { Crew } from '@/types/crew'
import { supabase, hasSupabaseCredentials } from '@/lib/supabase'

export function useActiveCrews() {
  const [crews, setCrews] = useState<Crew[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchActiveCrews()
  }, [])

  async function fetchActiveCrews() {
    try {
      setIsLoading(true)

      // If no Supabase credentials, use mock data immediately
      if (!hasSupabaseCredentials()) {
        console.log('Using mock data - Supabase credentials not configured')
        setMockData()
        return
      }

      const { data, error } = await supabase
        .from('crews')
        .select(`
          *,
          agents (*),
          tasks (*)
        `)
        .in('status', ['running', 'pending'])
        .order('updated_at', { ascending: false })
        .limit(10)

      if (error) throw error

      setCrews(data || [])
    } catch (error) {
      console.error('Error fetching active crews:', error)
      setMockData()
    } finally {
      setIsLoading(false)
    }
  }

  function setMockData() {
      // Mock data for demo
      setCrews([
        {
          id: '1',
          name: 'Research Crew',
          description: 'AI research and analysis team',
          status: 'running',
          progress: 75,
          model: 'Cerebras Llama3.1-70B',
          agents: [
            { id: '1', role: 'Researcher', goal: 'Research topics', backstory: 'Expert researcher', tools: ['web_search'] },
            { id: '2', role: 'Writer', goal: 'Write reports', backstory: 'Professional writer', tools: ['text_generation'] },
            { id: '3', role: 'Reviewer', goal: 'Review content', backstory: 'Quality reviewer', tools: ['analysis'] },
          ],
          tasks: [
            { id: '1', description: 'Research market trends', expected_output: 'Market analysis report', status: 'completed' },
            { id: '2', description: 'Write summary', expected_output: 'Executive summary', status: 'running' },
          ],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'Content Pipeline',
          description: 'Content creation and optimization',
          status: 'pending',
          progress: 25,
          model: 'Cerebras Llama3.1-8B',
          agents: [
            { id: '4', role: 'Content Creator', goal: 'Create content', backstory: 'Creative writer', tools: ['text_generation'] },
            { id: '5', role: 'SEO Optimizer', goal: 'Optimize for SEO', backstory: 'SEO expert', tools: ['seo_analysis'] },
          ],
          tasks: [
            { id: '3', description: 'Generate blog posts', expected_output: 'Blog content', status: 'pending' },
          ],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'Data Analysis',
          description: 'Data processing and insights',
          status: 'completed',
          progress: 100,
          model: 'Cerebras Llama3.1-70B',
          agents: [
            { id: '6', role: 'Data Analyst', goal: 'Analyze data', backstory: 'Data expert', tools: ['data_analysis'] },
          ],
          tasks: [
            { id: '4', description: 'Process dataset', expected_output: 'Analysis report', status: 'completed' },
          ],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
  }

  return { crews, isLoading, refetch: fetchActiveCrews }
}
