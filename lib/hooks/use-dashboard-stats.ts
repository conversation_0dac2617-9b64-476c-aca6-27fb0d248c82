import { useState, useEffect } from 'react'
import { DashboardStats } from '@/types/dashboard'
import { supabase, hasSupabaseCredentials } from '@/lib/supabase'

export function useDashboardStats() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  async function fetchStats() {
    try {
      setIsLoading(true)

      // If no Supabase credentials, use mock data
      if (!hasSupabaseCredentials()) {
        console.log('Using mock stats - Supabase credentials not configured')
        setStats({
          activeCrews: 3,
          totalTasks: 12,
          successRate: 94,
          averageSpeed: 2.3,
        })
        return
      }

      // Fetch active crews count
      const { count: activeCrews } = await supabase
        .from('crews')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'running')

      // Fetch total tasks count
      const { count: totalTasks } = await supabase
        .from('tasks')
        .select('*', { count: 'exact', head: true })

      // Fetch completed tasks for success rate
      const { count: completedTasks } = await supabase
        .from('tasks')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'completed')

      // Calculate success rate
      const successRate = totalTasks ? Math.round((completedTasks! / totalTasks) * 100) : 0

      // Mock average speed (would come from performance metrics in real app)
      const averageSpeed = 2.3

      setStats({
        activeCrews: activeCrews || 0,
        totalTasks: totalTasks || 0,
        successRate,
        averageSpeed,
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      // Set default stats on error
      setStats({
        activeCrews: 12,
        totalTasks: 147,
        successRate: 94,
        averageSpeed: 2.3,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return { stats, isLoading, refetch: fetchStats }
}
