import { createClient } from '@supabase/supabase-js'

// Use fallback values for development when env vars are not set
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// Create a mock client if no real credentials are provided
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper to check if we have real Supabase credentials
export const hasSupabaseCredentials = () => {
  return process.env.NEXT_PUBLIC_SUPABASE_URL &&
         process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
         process.env.NEXT_PUBLIC_SUPABASE_URL !== 'https://placeholder.supabase.co'
}

// Database schema types
export interface CrewRecord {
  id: string
  name: string
  description: string
  status: 'draft' | 'running' | 'completed' | 'failed' | 'pending'
  progress: number
  model: string
  agents: AgentRecord[]
  tasks: TaskRecord[]
  created_at: string
  updated_at: string
  user_id: string
}

export interface AgentRecord {
  id: string
  role: string
  goal: string
  backstory: string
  tools: string[]
  crew_id: string
}

export interface TaskRecord {
  id: string
  description: string
  expected_output: string
  agent_id: string
  crew_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
}

export interface ActivityRecord {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  description: string
  crew_name?: string
  timestamp: string
  user_id: string
}
