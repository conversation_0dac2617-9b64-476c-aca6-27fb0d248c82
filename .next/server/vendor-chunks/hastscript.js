"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/factory.js":
/*!********************************************!*\
  !*** ./node_modules/hastscript/factory.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar find = __webpack_require__(/*! property-information/find */ \"(ssr)/./node_modules/property-information/find.js\");\nvar normalize = __webpack_require__(/*! property-information/normalize */ \"(ssr)/./node_modules/property-information/normalize.js\");\nvar parseSelector = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/index.js\");\nvar spaces = (__webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\").parse);\nvar commas = (__webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\").parse);\nmodule.exports = factory;\nvar own = {}.hasOwnProperty;\nfunction factory(schema, defaultTagName, caseSensitive) {\n    var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null;\n    return h;\n    // Hyperscript compatible DSL for creating virtual hast trees.\n    function h(selector, properties) {\n        var node = parseSelector(selector, defaultTagName);\n        var children = Array.prototype.slice.call(arguments, 2);\n        var name = node.tagName.toLowerCase();\n        var property;\n        node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name;\n        if (properties && isChildren(properties, node)) {\n            children.unshift(properties);\n            properties = null;\n        }\n        if (properties) {\n            for(property in properties){\n                addProperty(node.properties, property, properties[property]);\n            }\n        }\n        addChild(node.children, children);\n        if (node.tagName === \"template\") {\n            node.content = {\n                type: \"root\",\n                children: node.children\n            };\n            node.children = [];\n        }\n        return node;\n    }\n    function addProperty(properties, key, value) {\n        var info;\n        var property;\n        var result;\n        // Ignore nullish and NaN values.\n        if (value === null || value === undefined || value !== value) {\n            return;\n        }\n        info = find(schema, key);\n        property = info.property;\n        result = value;\n        // Handle list values.\n        if (typeof result === \"string\") {\n            if (info.spaceSeparated) {\n                result = spaces(result);\n            } else if (info.commaSeparated) {\n                result = commas(result);\n            } else if (info.commaOrSpaceSeparated) {\n                result = spaces(commas(result).join(\" \"));\n            }\n        }\n        // Accept `object` on style.\n        if (property === \"style\" && typeof value !== \"string\") {\n            result = style(result);\n        }\n        // Class-names (which can be added both on the `selector` and here).\n        if (property === \"className\" && properties.className) {\n            result = properties.className.concat(result);\n        }\n        properties[property] = parsePrimitives(info, property, result);\n    }\n}\nfunction isChildren(value, node) {\n    return typeof value === \"string\" || \"length\" in value || isNode(node.tagName, value);\n}\nfunction isNode(tagName, value) {\n    var type = value.type;\n    if (tagName === \"input\" || !type || typeof type !== \"string\") {\n        return false;\n    }\n    if (typeof value.children === \"object\" && \"length\" in value.children) {\n        return true;\n    }\n    type = type.toLowerCase();\n    if (tagName === \"button\") {\n        return type !== \"menu\" && type !== \"submit\" && type !== \"reset\" && type !== \"button\";\n    }\n    return \"value\" in value;\n}\nfunction addChild(nodes, value) {\n    var index;\n    var length;\n    if (typeof value === \"string\" || typeof value === \"number\") {\n        nodes.push({\n            type: \"text\",\n            value: String(value)\n        });\n        return;\n    }\n    if (typeof value === \"object\" && \"length\" in value) {\n        index = -1;\n        length = value.length;\n        while(++index < length){\n            addChild(nodes, value[index]);\n        }\n        return;\n    }\n    if (typeof value !== \"object\" || !(\"type\" in value)) {\n        throw new Error(\"Expected node, nodes, or string, got `\" + value + \"`\");\n    }\n    nodes.push(value);\n}\n// Parse a (list of) primitives.\nfunction parsePrimitives(info, name, value) {\n    var index;\n    var length;\n    var result;\n    if (typeof value !== \"object\" || !(\"length\" in value)) {\n        return parsePrimitive(info, name, value);\n    }\n    length = value.length;\n    index = -1;\n    result = [];\n    while(++index < length){\n        result[index] = parsePrimitive(info, name, value[index]);\n    }\n    return result;\n}\n// Parse a single primitives.\nfunction parsePrimitive(info, name, value) {\n    var result = value;\n    if (info.number || info.positiveNumber) {\n        if (!isNaN(result) && result !== \"\") {\n            result = Number(result);\n        }\n    } else if (info.boolean || info.overloadedBoolean) {\n        // Accept `boolean` and `string`.\n        if (typeof result === \"string\" && (result === \"\" || normalize(value) === normalize(name))) {\n            result = true;\n        }\n    }\n    return result;\n}\nfunction style(value) {\n    var result = [];\n    var key;\n    for(key in value){\n        result.push([\n            key,\n            value[key]\n        ].join(\": \"));\n    }\n    return result.join(\"; \");\n}\nfunction createAdjustMap(values) {\n    var length = values.length;\n    var index = -1;\n    var result = {};\n    var value;\n    while(++index < length){\n        value = values[index];\n        result[value.toLowerCase()] = value;\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/factory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/html.js":
/*!*****************************************!*\
  !*** ./node_modules/hastscript/html.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar schema = __webpack_require__(/*! property-information/html */ \"(ssr)/./node_modules/property-information/html.js\");\nvar factory = __webpack_require__(/*! ./factory */ \"(ssr)/./node_modules/hastscript/factory.js\");\nvar html = factory(schema, \"div\");\nhtml.displayName = \"html\";\nmodule.exports = html;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9odG1sLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsU0FBU0MsbUJBQU9BLENBQUM7QUFDckIsSUFBSUMsVUFBVUQsbUJBQU9BLENBQUM7QUFFdEIsSUFBSUUsT0FBT0QsUUFBUUYsUUFBUTtBQUMzQkcsS0FBS0MsV0FBVyxHQUFHO0FBRW5CQyxPQUFPQyxPQUFPLEdBQUdIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3Jld2NyYWZ0LWFpLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2hhc3RzY3JpcHQvaHRtbC5qcz84YzdiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgc2NoZW1hID0gcmVxdWlyZSgncHJvcGVydHktaW5mb3JtYXRpb24vaHRtbCcpXG52YXIgZmFjdG9yeSA9IHJlcXVpcmUoJy4vZmFjdG9yeScpXG5cbnZhciBodG1sID0gZmFjdG9yeShzY2hlbWEsICdkaXYnKVxuaHRtbC5kaXNwbGF5TmFtZSA9ICdodG1sJ1xuXG5tb2R1bGUuZXhwb3J0cyA9IGh0bWxcbiJdLCJuYW1lcyI6WyJzY2hlbWEiLCJyZXF1aXJlIiwiZmFjdG9yeSIsImh0bWwiLCJkaXNwbGF5TmFtZSIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/index.js":
/*!******************************************!*\
  !*** ./node_modules/hastscript/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/hastscript/html.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSw2RkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9pbmRleC5qcz83YTE5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vaHRtbCcpXG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/index.js\n");

/***/ })

};
;