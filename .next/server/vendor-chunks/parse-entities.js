"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-entities";
exports.ids = ["vendor-chunks/parse-entities"];
exports.modules = {

/***/ "(ssr)/./node_modules/parse-entities/decode-entity.js":
/*!******************************************************!*\
  !*** ./node_modules/parse-entities/decode-entity.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar characterEntities = __webpack_require__(/*! character-entities */ \"(ssr)/./node_modules/character-entities/index.json\");\nmodule.exports = decodeEntity;\nvar own = {}.hasOwnProperty;\nfunction decodeEntity(characters) {\n    return own.call(characterEntities, characters) ? characterEntities[characters] : false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFyc2UtZW50aXRpZXMvZGVjb2RlLWVudGl0eS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLElBQUlBLG9CQUFvQkMsbUJBQU9BLENBQUM7QUFFaENDLE9BQU9DLE9BQU8sR0FBR0M7QUFFakIsSUFBSUMsTUFBTSxDQUFDLEVBQUVDLGNBQWM7QUFFM0IsU0FBU0YsYUFBYUcsVUFBVTtJQUM5QixPQUFPRixJQUFJRyxJQUFJLENBQUNSLG1CQUFtQk8sY0FDL0JQLGlCQUFpQixDQUFDTyxXQUFXLEdBQzdCO0FBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvcGFyc2UtZW50aXRpZXMvZGVjb2RlLWVudGl0eS5qcz9jMjc1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgY2hhcmFjdGVyRW50aXRpZXMgPSByZXF1aXJlKCdjaGFyYWN0ZXItZW50aXRpZXMnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGRlY29kZUVudGl0eVxuXG52YXIgb3duID0ge30uaGFzT3duUHJvcGVydHlcblxuZnVuY3Rpb24gZGVjb2RlRW50aXR5KGNoYXJhY3RlcnMpIHtcbiAgcmV0dXJuIG93bi5jYWxsKGNoYXJhY3RlckVudGl0aWVzLCBjaGFyYWN0ZXJzKVxuICAgID8gY2hhcmFjdGVyRW50aXRpZXNbY2hhcmFjdGVyc11cbiAgICA6IGZhbHNlXG59XG4iXSwibmFtZXMiOlsiY2hhcmFjdGVyRW50aXRpZXMiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsImRlY29kZUVudGl0eSIsIm93biIsImhhc093blByb3BlcnR5IiwiY2hhcmFjdGVycyIsImNhbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parse-entities/decode-entity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/parse-entities/index.js":
/*!**********************************************!*\
  !*** ./node_modules/parse-entities/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar legacy = __webpack_require__(/*! character-entities-legacy */ \"(ssr)/./node_modules/character-entities-legacy/index.json\");\nvar invalid = __webpack_require__(/*! character-reference-invalid */ \"(ssr)/./node_modules/character-reference-invalid/index.json\");\nvar decimal = __webpack_require__(/*! is-decimal */ \"(ssr)/./node_modules/is-decimal/index.js\");\nvar hexadecimal = __webpack_require__(/*! is-hexadecimal */ \"(ssr)/./node_modules/is-hexadecimal/index.js\");\nvar alphanumerical = __webpack_require__(/*! is-alphanumerical */ \"(ssr)/./node_modules/is-alphanumerical/index.js\");\nvar decodeEntity = __webpack_require__(/*! ./decode-entity */ \"(ssr)/./node_modules/parse-entities/decode-entity.js\");\nmodule.exports = parseEntities;\nvar own = {}.hasOwnProperty;\nvar fromCharCode = String.fromCharCode;\nvar noop = Function.prototype;\n// Default settings.\nvar defaults = {\n    warning: null,\n    reference: null,\n    text: null,\n    warningContext: null,\n    referenceContext: null,\n    textContext: null,\n    position: {},\n    additional: null,\n    attribute: false,\n    nonTerminated: true\n};\n// Characters.\nvar tab = 9 // '\\t'\n;\nvar lineFeed = 10 // '\\n'\n;\nvar formFeed = 12 // '\\f'\n;\nvar space = 32 // ' '\n;\nvar ampersand = 38 // '&'\n;\nvar semicolon = 59 // ';'\n;\nvar lessThan = 60 // '<'\n;\nvar equalsTo = 61 // '='\n;\nvar numberSign = 35 // '#'\n;\nvar uppercaseX = 88 // 'X'\n;\nvar lowercaseX = 120 // 'x'\n;\nvar replacementCharacter = 65533 // '�'\n;\n// Reference types.\nvar name = \"named\";\nvar hexa = \"hexadecimal\";\nvar deci = \"decimal\";\n// Map of bases.\nvar bases = {};\nbases[hexa] = 16;\nbases[deci] = 10;\n// Map of types to tests.\n// Each type of character reference accepts different characters.\n// This test is used to detect whether a reference has ended (as the semicolon\n// is not strictly needed).\nvar tests = {};\ntests[name] = alphanumerical;\ntests[deci] = decimal;\ntests[hexa] = hexadecimal;\n// Warning types.\nvar namedNotTerminated = 1;\nvar numericNotTerminated = 2;\nvar namedEmpty = 3;\nvar numericEmpty = 4;\nvar namedUnknown = 5;\nvar numericDisallowed = 6;\nvar numericProhibited = 7;\n// Warning messages.\nvar messages = {};\nmessages[namedNotTerminated] = \"Named character references must be terminated by a semicolon\";\nmessages[numericNotTerminated] = \"Numeric character references must be terminated by a semicolon\";\nmessages[namedEmpty] = \"Named character references cannot be empty\";\nmessages[numericEmpty] = \"Numeric character references cannot be empty\";\nmessages[namedUnknown] = \"Named character references must be known\";\nmessages[numericDisallowed] = \"Numeric character references cannot be disallowed\";\nmessages[numericProhibited] = \"Numeric character references cannot be outside the permissible Unicode range\";\n// Wrap to ensure clean parameters are given to `parse`.\nfunction parseEntities(value, options) {\n    var settings = {};\n    var option;\n    var key;\n    if (!options) {\n        options = {};\n    }\n    for(key in defaults){\n        option = options[key];\n        settings[key] = option === null || option === undefined ? defaults[key] : option;\n    }\n    if (settings.position.indent || settings.position.start) {\n        settings.indent = settings.position.indent || [];\n        settings.position = settings.position.start;\n    }\n    return parse(value, settings);\n}\n// Parse entities.\n// eslint-disable-next-line complexity\nfunction parse(value, settings) {\n    var additional = settings.additional;\n    var nonTerminated = settings.nonTerminated;\n    var handleText = settings.text;\n    var handleReference = settings.reference;\n    var handleWarning = settings.warning;\n    var textContext = settings.textContext;\n    var referenceContext = settings.referenceContext;\n    var warningContext = settings.warningContext;\n    var pos = settings.position;\n    var indent = settings.indent || [];\n    var length = value.length;\n    var index = 0;\n    var lines = -1;\n    var column = pos.column || 1;\n    var line = pos.line || 1;\n    var queue = \"\";\n    var result = [];\n    var entityCharacters;\n    var namedEntity;\n    var terminated;\n    var characters;\n    var character;\n    var reference;\n    var following;\n    var warning;\n    var reason;\n    var output;\n    var entity;\n    var begin;\n    var start;\n    var type;\n    var test;\n    var prev;\n    var next;\n    var diff;\n    var end;\n    if (typeof additional === \"string\") {\n        additional = additional.charCodeAt(0);\n    }\n    // Cache the current point.\n    prev = now();\n    // Wrap `handleWarning`.\n    warning = handleWarning ? parseError : noop;\n    // Ensure the algorithm walks over the first character and the end\n    // (inclusive).\n    index--;\n    length++;\n    while(++index < length){\n        // If the previous character was a newline.\n        if (character === lineFeed) {\n            column = indent[lines] || 1;\n        }\n        character = value.charCodeAt(index);\n        if (character === ampersand) {\n            following = value.charCodeAt(index + 1);\n            // The behaviour depends on the identity of the next character.\n            if (following === tab || following === lineFeed || following === formFeed || following === space || following === ampersand || following === lessThan || following !== following || additional && following === additional) {\n                // Not a character reference.\n                // No characters are consumed, and nothing is returned.\n                // This is not an error, either.\n                queue += fromCharCode(character);\n                column++;\n                continue;\n            }\n            start = index + 1;\n            begin = start;\n            end = start;\n            if (following === numberSign) {\n                // Numerical entity.\n                end = ++begin;\n                // The behaviour further depends on the next character.\n                following = value.charCodeAt(end);\n                if (following === uppercaseX || following === lowercaseX) {\n                    // ASCII hex digits.\n                    type = hexa;\n                    end = ++begin;\n                } else {\n                    // ASCII digits.\n                    type = deci;\n                }\n            } else {\n                // Named entity.\n                type = name;\n            }\n            entityCharacters = \"\";\n            entity = \"\";\n            characters = \"\";\n            test = tests[type];\n            end--;\n            while(++end < length){\n                following = value.charCodeAt(end);\n                if (!test(following)) {\n                    break;\n                }\n                characters += fromCharCode(following);\n                // Check if we can match a legacy named reference.\n                // If so, we cache that as the last viable named reference.\n                // This ensures we do not need to walk backwards later.\n                if (type === name && own.call(legacy, characters)) {\n                    entityCharacters = characters;\n                    entity = legacy[characters];\n                }\n            }\n            terminated = value.charCodeAt(end) === semicolon;\n            if (terminated) {\n                end++;\n                namedEntity = type === name ? decodeEntity(characters) : false;\n                if (namedEntity) {\n                    entityCharacters = characters;\n                    entity = namedEntity;\n                }\n            }\n            diff = 1 + end - start;\n            if (!terminated && !nonTerminated) {\n            // Empty.\n            } else if (!characters) {\n                // An empty (possible) entity is valid, unless it’s numeric (thus an\n                // ampersand followed by an octothorp).\n                if (type !== name) {\n                    warning(numericEmpty, diff);\n                }\n            } else if (type === name) {\n                // An ampersand followed by anything unknown, and not terminated, is\n                // invalid.\n                if (terminated && !entity) {\n                    warning(namedUnknown, 1);\n                } else {\n                    // If theres something after an entity name which is not known, cap\n                    // the reference.\n                    if (entityCharacters !== characters) {\n                        end = begin + entityCharacters.length;\n                        diff = 1 + end - begin;\n                        terminated = false;\n                    }\n                    // If the reference is not terminated, warn.\n                    if (!terminated) {\n                        reason = entityCharacters ? namedNotTerminated : namedEmpty;\n                        if (settings.attribute) {\n                            following = value.charCodeAt(end);\n                            if (following === equalsTo) {\n                                warning(reason, diff);\n                                entity = null;\n                            } else if (alphanumerical(following)) {\n                                entity = null;\n                            } else {\n                                warning(reason, diff);\n                            }\n                        } else {\n                            warning(reason, diff);\n                        }\n                    }\n                }\n                reference = entity;\n            } else {\n                if (!terminated) {\n                    // All non-terminated numeric entities are not rendered, and trigger a\n                    // warning.\n                    warning(numericNotTerminated, diff);\n                }\n                // When terminated and number, parse as either hexadecimal or decimal.\n                reference = parseInt(characters, bases[type]);\n                // Trigger a warning when the parsed number is prohibited, and replace\n                // with replacement character.\n                if (prohibited(reference)) {\n                    warning(numericProhibited, diff);\n                    reference = fromCharCode(replacementCharacter);\n                } else if (reference in invalid) {\n                    // Trigger a warning when the parsed number is disallowed, and replace\n                    // by an alternative.\n                    warning(numericDisallowed, diff);\n                    reference = invalid[reference];\n                } else {\n                    // Parse the number.\n                    output = \"\";\n                    // Trigger a warning when the parsed number should not be used.\n                    if (disallowed(reference)) {\n                        warning(numericDisallowed, diff);\n                    }\n                    // Stringify the number.\n                    if (reference > 0xffff) {\n                        reference -= 0x10000;\n                        output += fromCharCode(reference >>> (10 & 0x3ff) | 0xd800);\n                        reference = 0xdc00 | reference & 0x3ff;\n                    }\n                    reference = output + fromCharCode(reference);\n                }\n            }\n            // Found it!\n            // First eat the queued characters as normal text, then eat an entity.\n            if (reference) {\n                flush();\n                prev = now();\n                index = end - 1;\n                column += end - start + 1;\n                result.push(reference);\n                next = now();\n                next.offset++;\n                if (handleReference) {\n                    handleReference.call(referenceContext, reference, {\n                        start: prev,\n                        end: next\n                    }, value.slice(start - 1, end));\n                }\n                prev = next;\n            } else {\n                // If we could not find a reference, queue the checked characters (as\n                // normal characters), and move the pointer to their end.\n                // This is possible because we can be certain neither newlines nor\n                // ampersands are included.\n                characters = value.slice(start - 1, end);\n                queue += characters;\n                column += characters.length;\n                index = end - 1;\n            }\n        } else {\n            // Handle anything other than an ampersand, including newlines and EOF.\n            if (character === 10 // Line feed\n            ) {\n                line++;\n                lines++;\n                column = 0;\n            }\n            if (character === character) {\n                queue += fromCharCode(character);\n                column++;\n            } else {\n                flush();\n            }\n        }\n    }\n    // Return the reduced nodes.\n    return result.join(\"\");\n    // Get current position.\n    function now() {\n        return {\n            line: line,\n            column: column,\n            offset: index + (pos.offset || 0)\n        };\n    }\n    // “Throw” a parse-error: a warning.\n    function parseError(code, offset) {\n        var position = now();\n        position.column += offset;\n        position.offset += offset;\n        handleWarning.call(warningContext, messages[code], position, code);\n    }\n    // Flush `queue` (normal text).\n    // Macro invoked before each entity and at the end of `value`.\n    // Does nothing when `queue` is empty.\n    function flush() {\n        if (queue) {\n            result.push(queue);\n            if (handleText) {\n                handleText.call(textContext, queue, {\n                    start: prev,\n                    end: now()\n                });\n            }\n            queue = \"\";\n        }\n    }\n}\n// Check if `character` is outside the permissible unicode range.\nfunction prohibited(code) {\n    return code >= 0xd800 && code <= 0xdfff || code > 0x10ffff;\n}\n// Check if `character` is disallowed.\nfunction disallowed(code) {\n    return code >= 0x0001 && code <= 0x0008 || code === 0x000b || code >= 0x000d && code <= 0x001f || code >= 0x007f && code <= 0x009f || code >= 0xfdd0 && code <= 0xfdef || (code & 0xffff) === 0xffff || (code & 0xffff) === 0xfffe;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parse-entities/index.js\n");

/***/ })

};
;