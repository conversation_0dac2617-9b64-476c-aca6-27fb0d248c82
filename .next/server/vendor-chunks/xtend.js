/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xtend";
exports.ids = ["vendor-chunks/xtend"];
exports.modules = {

/***/ "(ssr)/./node_modules/xtend/immutable.js":
/*!*****************************************!*\
  !*** ./node_modules/xtend/immutable.js ***!
  \*****************************************/
/***/ ((module) => {

eval("module.exports = extend;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction extend() {\n    var target = {};\n    for(var i = 0; i < arguments.length; i++){\n        var source = arguments[i];\n        for(var key in source){\n            if (hasOwnProperty.call(source, key)) {\n                target[key] = source[key];\n            }\n        }\n    }\n    return target;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMveHRlbmQvaW1tdXRhYmxlLmpzP2EwNTgiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBleHRlbmRcblxudmFyIGhhc093blByb3BlcnR5ID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eTtcblxuZnVuY3Rpb24gZXh0ZW5kKCkge1xuICAgIHZhciB0YXJnZXQgPSB7fVxuXG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50c1tpXVxuXG4gICAgICAgIGZvciAodmFyIGtleSBpbiBzb3VyY2UpIHtcbiAgICAgICAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKHNvdXJjZSwga2V5KSkge1xuICAgICAgICAgICAgICAgIHRhcmdldFtrZXldID0gc291cmNlW2tleV1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB0YXJnZXRcbn1cbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiZXh0ZW5kIiwiaGFzT3duUHJvcGVydHkiLCJPYmplY3QiLCJwcm90b3R5cGUiLCJ0YXJnZXQiLCJpIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwic291cmNlIiwia2V5IiwiY2FsbCJdLCJtYXBwaW5ncyI6IkFBQUFBLE9BQU9DLE9BQU8sR0FBR0M7QUFFakIsSUFBSUMsaUJBQWlCQyxPQUFPQyxTQUFTLENBQUNGLGNBQWM7QUFFcEQsU0FBU0Q7SUFDTCxJQUFJSSxTQUFTLENBQUM7SUFFZCxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUMsVUFBVUMsTUFBTSxFQUFFRixJQUFLO1FBQ3ZDLElBQUlHLFNBQVNGLFNBQVMsQ0FBQ0QsRUFBRTtRQUV6QixJQUFLLElBQUlJLE9BQU9ELE9BQVE7WUFDcEIsSUFBSVAsZUFBZVMsSUFBSSxDQUFDRixRQUFRQyxNQUFNO2dCQUNsQ0wsTUFBTSxDQUFDSyxJQUFJLEdBQUdELE1BQU0sQ0FBQ0MsSUFBSTtZQUM3QjtRQUNKO0lBQ0o7SUFFQSxPQUFPTDtBQUNYIiwiZmlsZSI6Iihzc3IpLy4vbm9kZV9tb2R1bGVzL3h0ZW5kL2ltbXV0YWJsZS5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xtend/immutable.js\n");

/***/ })

};
;