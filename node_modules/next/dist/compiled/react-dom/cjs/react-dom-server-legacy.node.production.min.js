/*
 React
 react-dom-server-legacy.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var aa=require("next/dist/compiled/react"),da=require("react-dom"),ja=require("stream");
function ka(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}
var r=Object.assign,y=Object.prototype.hasOwnProperty,la=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ya={},za={};
function Aa(a){if(y.call(za,a))return!0;if(y.call(ya,a))return!1;if(la.test(a))return za[a]=!0;ya[a]=!0;return!1}
var Ba=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ca=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ia=/["'&<>]/;
function z(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ia.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ja=/([A-Z])/g,Ka=/^ms-/,La=Array.isArray,Ma=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Na={pending:!1,data:null,method:null,action:null},Oa=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,lb={prefetchDNS:eb,preconnect:fb,preload:gb,preloadModule:hb,preinitStyle:ib,preinitScript:jb,preinitModuleScript:kb},B=[],mb=/(<\/|<)(s)(cript)/gi;function nb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function ob(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function E(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function pb(a){return E("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function zb(a,b,c){switch(b){case "noscript":return E(2,null,a.tagScope|1);case "select":return E(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return E(3,null,a.tagScope);case "picture":return E(2,null,a.tagScope|2);case "math":return E(4,null,a.tagScope);case "foreignObject":return E(2,null,a.tagScope);case "table":return E(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return E(6,null,a.tagScope);case "colgroup":return E(8,null,a.tagScope);case "tr":return E(7,null,a.tagScope)}return 5<=
a.insertionMode?E(2,null,a.tagScope):0===a.insertionMode?"html"===b?E(1,null,a.tagScope):E(2,null,a.tagScope):1===a.insertionMode?E(2,null,a.tagScope):a}var Ab=new Map;
function Bb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(y.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=z(d);e=z((""+e).trim())}else f=Ab.get(d),void 0===f&&(f=z(d.replace(Ja,"-$1").toLowerCase().replace(Ka,"-ms-")),Ab.set(d,f)),e="number"===typeof e?0===e||Ba.has(d)?""+e:e+"px":
z((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function Cb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function H(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',z(c),'"')}function Db(a){var b=a.nextFormID++;return a.idPrefix+b}var Eb=z("javascript:throw new Error('A React form was unexpectedly submitted.')");
function Fb(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");H(this,"name",b);H(this,"value",a);this.push("/>")}
function Gb(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Db(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',Eb,'"'),g=f=e=d=h=null,Hb(b,c)));null!=h&&I(a,"name",h);null!=d&&I(a,"formAction",d);null!=e&&I(a,"formEncType",e);null!=f&&I(a,"formMethod",f);null!=g&&I(a,"formTarget",g);return k}
function I(a,b,c){switch(b){case "className":H(a,"class",c);break;case "tabIndex":H(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":H(a,b,c);break;case "style":Bb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',z(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Cb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',z(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',z(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',z(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',z(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',z(c),'"');break;case "xlinkActuate":H(a,"xlink:actuate",
c);break;case "xlinkArcrole":H(a,"xlink:arcrole",c);break;case "xlinkRole":H(a,"xlink:role",c);break;case "xlinkShow":H(a,"xlink:show",c);break;case "xlinkTitle":H(a,"xlink:title",c);break;case "xlinkType":H(a,"xlink:type",c);break;case "xmlBase":H(a,"xml:base",c);break;case "xmlLang":H(a,"xml:lang",c);break;case "xmlSpace":H(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ca.get(b)||b,Aa(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',z(c),'"')}}}function K(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function Ib(a){var b="";aa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function Hb(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});',"\x3c/script>"))}
function Jb(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return L(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return L(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:z(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:r({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Kb(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return L(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return L(d.preconnectChunks,b);case "preload":return L(d.preloadChunks,
b);default:return L(d.hoistableChunks,b)}}function L(a,b){a.push(M("link"));for(var c in b)if(y.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:I(a,c,d)}}a.push("/>");return null}
function Lb(a,b,c){a.push(M(c));for(var d in b)if(y.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:I(a,d,e)}}a.push("/>");return null}
function Mb(a,b){a.push(M("title"));var c=null,d=null,e;for(e in b)if(y.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:I(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(z(""+b));K(a,d,c);a.push(Nb("title"));return null}
function Ob(a,b){a.push(M("script"));var c=null,d=null,e;for(e in b)if(y.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:I(a,e,f)}}a.push(">");K(a,d,c);"string"===typeof c&&a.push(z(c));a.push(Nb("script"));return null}
function Pb(a,b,c){a.push(M(c));var d=c=null,e;for(e in b)if(y.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:I(a,e,f)}}a.push(">");K(a,d,c);return"string"===typeof c?(a.push(z(c)),null):c}var Qb=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Rb=new Map;function M(a){var b=Rb.get(a);if(void 0===b){if(!Qb.test(a))throw Error("Invalid tag: "+a);b="<"+a;Rb.set(a,b)}return b}
function Sb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(M("select"));var h=null,k=null,l;for(l in c)if(y.call(c,l)){var n=c[l];if(null!=n)switch(l){case "children":h=n;break;case "dangerouslySetInnerHTML":k=n;break;case "defaultValue":case "value":break;default:I(a,l,n)}}a.push(">");K(a,k,h);return h;case "option":var q=f.selectedValue;a.push(M("option"));var m=null,w=null,C=null,N=null,u;for(u in c)if(y.call(c,
u)){var t=c[u];if(null!=t)switch(u){case "children":m=t;break;case "selected":C=t;break;case "dangerouslySetInnerHTML":N=t;break;case "value":w=t;default:I(a,u,t)}}if(null!=q){var p=null!==w?""+w:Ib(m);if(La(q))for(var F=0;F<q.length;F++){if(""+q[F]===p){a.push(' selected=""');break}}else""+q===p&&a.push(' selected=""')}else C&&a.push(' selected=""');a.push(">");K(a,N,m);return m;case "textarea":a.push(M("textarea"));var x=null,v=null,D=null,O;for(O in c)if(y.call(c,O)){var A=c[O];if(null!=A)switch(O){case "children":D=
A;break;case "value":x=A;break;case "defaultValue":v=A;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:I(a,O,A)}}null===x&&null!==v&&(x=v);a.push(">");if(null!=D){if(null!=x)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(La(D)){if(1<D.length)throw Error("<textarea> can only have at most one child.");x=""+D[0]}x=""+D}"string"===typeof x&&"\n"===x[0]&&a.push("\n");null!==x&&a.push(z(""+x));
return null;case "input":a.push(M("input"));var ea=null,U=null,P=null,ma=null,fa=null,X=null,Pa=null,Qa=null,Ra=null,na;for(na in c)if(y.call(c,na)){var R=c[na];if(null!=R)switch(na){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":ea=R;break;case "formAction":U=R;break;case "formEncType":P=R;break;case "formMethod":ma=R;break;case "formTarget":fa=R;break;case "defaultChecked":Ra=
R;break;case "defaultValue":Pa=R;break;case "checked":Qa=R;break;case "value":X=R;break;default:I(a,na,R)}}var qb=Gb(a,d,e,U,P,ma,fa,ea);null!==Qa?Cb(a,"checked",Qa):null!==Ra&&Cb(a,"checked",Ra);null!==X?I(a,"value",X):null!==Pa&&I(a,"value",Pa);a.push("/>");null!==qb&&qb.forEach(Fb,a);return null;case "button":a.push(M("button"));var oa=null,pa=null,ba=null,qa=null,ra=null,Sa=null,sa=null,Ta;for(Ta in c)if(y.call(c,Ta)){var ca=c[Ta];if(null!=ca)switch(Ta){case "children":oa=ca;break;case "dangerouslySetInnerHTML":pa=
ca;break;case "name":ba=ca;break;case "formAction":qa=ca;break;case "formEncType":ra=ca;break;case "formMethod":Sa=ca;break;case "formTarget":sa=ca;break;default:I(a,Ta,ca)}}var Oc=Gb(a,d,e,qa,ra,Sa,sa,ba);a.push(">");null!==Oc&&Oc.forEach(Fb,a);K(a,pa,oa);if("string"===typeof oa){a.push(z(oa));var Pc=null}else Pc=oa;return Pc;case "form":a.push(M("form"));var Ua=null,Qc=null,ha=null,Va=null,Wa=null,Xa=null,Ya;for(Ya in c)if(y.call(c,Ya)){var ia=c[Ya];if(null!=ia)switch(Ya){case "children":Ua=ia;
break;case "dangerouslySetInnerHTML":Qc=ia;break;case "action":ha=ia;break;case "encType":Va=ia;break;case "method":Wa=ia;break;case "target":Xa=ia;break;default:I(a,Ya,ia)}}var Wb=null,Xb=null;if("function"===typeof ha)if("function"===typeof ha.$$FORM_ACTION){var ze=Db(d),Da=ha.$$FORM_ACTION(ze);ha=Da.action||"";Va=Da.encType;Wa=Da.method;Xa=Da.target;Wb=Da.data;Xb=Da.name}else a.push(" ","action",'="',Eb,'"'),Xa=Wa=Va=ha=null,Hb(d,e);null!=ha&&I(a,"action",ha);null!=Va&&I(a,"encType",Va);null!=
Wa&&I(a,"method",Wa);null!=Xa&&I(a,"target",Xa);a.push(">");null!==Xb&&(a.push('<input type="hidden"'),H(a,"name",Xb),a.push("/>"),null!==Wb&&Wb.forEach(Fb,a));K(a,Qc,Ua);if("string"===typeof Ua){a.push(z(Ua));var Rc=null}else Rc=Ua;return Rc;case "menuitem":a.push(M("menuitem"));for(var rb in c)if(y.call(c,rb)){var Sc=c[rb];if(null!=Sc)switch(rb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:I(a,rb,Sc)}}a.push(">");
return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Tc=Mb(a,c);else Mb(e.hoistableChunks,c),Tc=null;return Tc;case "link":return Jb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var Yb=c.async;if("string"!==typeof c.src||!c.src||!Yb||"function"===typeof Yb||"symbol"===typeof Yb||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Uc=Ob(a,c);else{var sb=c.src;if("module"===c.type){var tb=d.moduleScriptResources;var Vc=e.preloads.moduleScripts}else tb=
d.scriptResources,Vc=e.preloads.scripts;var ub=tb.hasOwnProperty(sb)?tb[sb]:void 0;if(null!==ub){tb[sb]=null;var Zb=c;if(ub){2===ub.length&&(Zb=r({},c),Kb(Zb,ub));var Wc=Vc.get(sb);Wc&&(Wc.length=0)}var Xc=[];e.scripts.add(Xc);Ob(Xc,Zb)}g&&a.push("\x3c!-- --\x3e");Uc=null}return Uc;case "style":var vb=c.precedence,ta=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof vb||"string"!==typeof ta||""===ta){a.push(M("style"));var Ea=null,Yc=null,Za;for(Za in c)if(y.call(c,
Za)){var wb=c[Za];if(null!=wb)switch(Za){case "children":Ea=wb;break;case "dangerouslySetInnerHTML":Yc=wb;break;default:I(a,Za,wb)}}a.push(">");var $a=Array.isArray(Ea)?2>Ea.length?Ea[0]:null:Ea;"function"!==typeof $a&&"symbol"!==typeof $a&&null!==$a&&void 0!==$a&&a.push(z(""+$a));K(a,Yc,Ea);a.push(Nb("style"));var Zc=null}else{var ua=e.styles.get(vb);if(null!==(d.styleResources.hasOwnProperty(ta)?d.styleResources[ta]:void 0)){d.styleResources[ta]=null;ua?ua.hrefs.push(z(ta)):(ua={precedence:z(vb),
rules:[],hrefs:[z(ta)],sheets:new Map},e.styles.set(vb,ua));var $c=ua.rules,Fa=null,ad=null,xb;for(xb in c)if(y.call(c,xb)){var $b=c[xb];if(null!=$b)switch(xb){case "children":Fa=$b;break;case "dangerouslySetInnerHTML":ad=$b}}var ab=Array.isArray(Fa)?2>Fa.length?Fa[0]:null:Fa;"function"!==typeof ab&&"symbol"!==typeof ab&&null!==ab&&void 0!==ab&&$c.push(z(""+ab));K($c,ad,Fa)}ua&&e.boundaryResources&&e.boundaryResources.styles.add(ua);g&&a.push("\x3c!-- --\x3e");Zc=void 0}return Zc;case "meta":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var bd=Lb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),bd="string"===typeof c.charSet?Lb(e.charsetChunks,c,"meta"):"viewport"===c.name?Lb(e.preconnectChunks,c,"meta"):Lb(e.hoistableChunks,c,"meta");return bd;case "listing":case "pre":a.push(M(b));var bb=null,cb=null,db;for(db in c)if(y.call(c,db)){var yb=c[db];if(null!=yb)switch(db){case "children":bb=yb;break;case "dangerouslySetInnerHTML":cb=yb;break;default:I(a,db,yb)}}a.push(">");if(null!=cb){if(null!=
bb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof cb||!("__html"in cb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var va=cb.__html;null!==va&&void 0!==va&&("string"===typeof va&&0<va.length&&"\n"===va[0]?a.push("\n",va):a.push(""+va))}"string"===typeof bb&&"\n"===bb[0]&&a.push("\n");return bb;case "img":var J=c.src,
G=c.srcSet;if(!("lazy"===c.loading||!J&&!G||"string"!==typeof J&&null!=J||"string"!==typeof G&&null!=G)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof J||":"!==J[4]||"d"!==J[0]&&"D"!==J[0]||"a"!==J[1]&&"A"!==J[1]||"t"!==J[2]&&"T"!==J[2]||"a"!==J[3]&&"A"!==J[3])&&("string"!==typeof G||":"!==G[4]||"d"!==G[0]&&"D"!==G[0]||"a"!==G[1]&&"A"!==G[1]||"t"!==G[2]&&"T"!==G[2]||"a"!==G[3]&&"A"!==G[3])){var cd="string"===typeof c.sizes?c.sizes:void 0,Ga=G?G+"\n"+(cd||""):J,ac=e.preloads.images,
wa=ac.get(Ga);if(wa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)ac.delete(Ga),e.highImagePreloads.add(wa)}else if(!d.imageResources.hasOwnProperty(Ga)){d.imageResources[Ga]=B;var bc=c.crossOrigin;var dd="string"===typeof bc?"use-credentials"===bc?bc:"":void 0;var Y=e.headers,cc;Y&&0<Y.remainingCapacity&&("high"===c.fetchPriority||500>Y.highImagePreloads.length)&&(cc=Tb(J,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:dd,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,
referrerPolicy:c.refererPolicy}),2<=(Y.remainingCapacity-=cc.length))?(e.resets.image[Ga]=B,Y.highImagePreloads&&(Y.highImagePreloads+=", "),Y.highImagePreloads+=cc):(wa=[],L(wa,{rel:"preload",as:"image",href:G?void 0:J,imageSrcSet:G,imageSizes:cd,crossOrigin:dd,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(wa):(e.bulkPreloads.add(wa),ac.set(Ga,wa)))}}return Lb(a,c,"img");
case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Lb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var ed=Pb(e.headChunks,c,"head")}else ed=Pb(a,c,"head");return ed;case "html":if(0===f.insertionMode&&null===
e.htmlChunks){e.htmlChunks=[""];var fd=Pb(e.htmlChunks,c,"html")}else fd=Pb(a,c,"html");return fd;default:if(-1!==b.indexOf("-")){a.push(M(b));var dc=null,gd=null,Ha;for(Ha in c)if(y.call(c,Ha)){var xa=c[Ha];if(null!=xa){var Ae=Ha;switch(Ha){case "children":dc=xa;break;case "dangerouslySetInnerHTML":gd=xa;break;case "style":Bb(a,xa);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:Aa(Ha)&&"function"!==typeof xa&&"symbol"!==typeof xa&&a.push(" ",Ae,'="',z(xa),
'"')}}}a.push(">");K(a,gd,dc);return dc}}return Pb(a,c,b)}var Ub=new Map;function Nb(a){var b=Ub.get(a);void 0===b&&(b="</"+a+">",Ub.set(a,b));return b}function Vb(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}
function ec(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function fc(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function gc(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var hc=/[<\u2028\u2029]/g;
function ic(a){return JSON.stringify(a).replace(hc,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var jc=/[&><\u2028\u2029]/g;
function kc(a){return JSON.stringify(a).replace(jc,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var lc=!1,mc=!0;
function nc(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);mc=this.push("</style>");lc=!0;b.length=0;c.length=0}}function oc(a){return 2!==a.state?lc=!0:!1}function pc(a,b,c){lc=!1;mc=!0;b.styles.forEach(nc,a);b.stylesheets.forEach(oc);lc&&(c.stylesToHoist=!0);return mc}
function Q(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var qc=[];function rc(a){L(qc,a.props);for(var b=0;b<qc.length;b++)this.push(qc[b]);qc.length=0;a.state=2}
function sc(a){var b=0<a.sheets.size;a.sheets.forEach(rc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function tc(a){if(0===a.state){a.state=1;var b=a.props;L(qc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<qc.length;a++)this.push(qc[a]);qc.length=0}}function uc(a){a.sheets.forEach(tc,this);a.sheets.clear()}
function vc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=kc(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=kc(""+d.props.href);a.push(g);e=""+e;a.push(",");e=kc(e);a.push(e);for(var h in f)if(y.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!Aa(h))break a;g=""+g}e.push(",");k=kc(k);e.push(k);e.push(",");g=
kc(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function wc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=z(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=z(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=z(JSON.stringify(e));a.push(e);for(var h in f)if(y.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!Aa(h))break a;g=""+g}e.push(",");k=z(JSON.stringify(k));e.push(k);
e.push(",");g=z(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function eb(a){var b=S?S:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(xc,yc)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],L(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}zc(b)}}}
function fb(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(xc,yc)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace(Ac,Bc);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],L(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}zc(c)}}}
function gb(a,b,c){var d=S?S:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var l=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(l))return;e.imageResources[l]=B;e=f.headers;var n;e&&0<e.remainingCapacity&&"high"===k&&(n=Tb(a,b,c),2<=(e.remainingCapacity-=n.length))?(f.resets.image[l]=B,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=n):(e=[],L(e,r({rel:"preload",href:g?
void 0:a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(l,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];L(g,r({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?B:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
L(g,r({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?B:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=B;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(l=Tb(a,b,c),2<=(e.remainingCapacity-=l.length)))f.resets.font[a]=B,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=l;else switch(e=[],
a=r({rel:"preload",href:a,as:b},c),L(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}zc(d)}}}
function hb(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?B:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=B}L(f,r({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);zc(c)}}}
function ib(a,b,c){var d=S?S:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:z(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:r({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Kb(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),zc(d))}}}
function jb(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=r({src:a,async:!0},b),f&&(2===f.length&&Kb(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Ob(a,b),zc(c))}}}
function kb(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=r({src:a,type:"module",async:!0},b),f&&(2===f.length&&Kb(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Ob(a,b),zc(c))}}}function Kb(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Tb(a,b,c){a=(""+a).replace(xc,yc);b=(""+b).replace(Ac,Bc);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)y.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Ac,Bc)+'"'));return b}var xc=/[<>\r\n]/g;
function yc(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Ac=/["';,\r\n]/g;
function Bc(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function Cc(a){this.styles.add(a)}function Dc(a){this.stylesheets.add(a)}
function Ec(a,b){var c=a.idPrefix,d=[],e=a.bootstrapScriptContent,f=a.bootstrapScripts,g=a.bootstrapModules;void 0!==e&&d.push("<script>",(""+e).replace(mb,nb),"\x3c/script>");e=c+"P:";var h=c+"S:";c+="B:";var k=new Set,l=new Set,n=new Set,q=new Map,m=new Set,w=new Set,C=new Set,N={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};if(void 0!==f)for(var u=0;u<f.length;u++){var t=f[u],p,F=void 0,x=void 0,v={rel:"preload",as:"script",fetchPriority:"low",nonce:void 0};"string"===
typeof t?v.href=p=t:(v.href=p=t.src,v.integrity=x="string"===typeof t.integrity?t.integrity:void 0,v.crossOrigin=F="string"===typeof t||null==t.crossOrigin?void 0:"use-credentials"===t.crossOrigin?"use-credentials":"");t=a;var D=p;t.scriptResources[D]=null;t.moduleScriptResources[D]=null;t=[];L(t,v);m.add(t);d.push('<script src="',z(p));"string"===typeof x&&d.push('" integrity="',z(x));"string"===typeof F&&d.push('" crossorigin="',z(F));d.push('" async="">\x3c/script>')}if(void 0!==g)for(f=0;f<g.length;f++)v=
g[f],F=p=void 0,x={rel:"modulepreload",fetchPriority:"low",nonce:void 0},"string"===typeof v?x.href=u=v:(x.href=u=v.src,x.integrity=F="string"===typeof v.integrity?v.integrity:void 0,x.crossOrigin=p="string"===typeof v||null==v.crossOrigin?void 0:"use-credentials"===v.crossOrigin?"use-credentials":""),v=a,t=u,v.scriptResources[t]=null,v.moduleScriptResources[t]=null,v=[],L(v,x),m.add(v),d.push('<script type="module" src="',z(u)),"string"===typeof F&&d.push('" integrity="',z(F)),"string"===typeof p&&
d.push('" crossorigin="',z(p)),d.push('" async="">\x3c/script>');return{placeholderPrefix:e,segmentPrefix:h,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:d,onHeaders:void 0,headers:null,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:k,fontPreloads:l,highImagePreloads:n,styles:q,
bootstrapScripts:m,scripts:w,bulkPreloads:C,preloads:N,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function Fc(a,b,c,d){if(c.generateStaticMarkup)return a.push(z(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(z(b)),a=!0);return a}
var Gc=Symbol.for("react.element"),Hc=Symbol.for("react.portal"),Ic=Symbol.for("react.fragment"),Jc=Symbol.for("react.strict_mode"),Kc=Symbol.for("react.profiler"),Lc=Symbol.for("react.provider"),Mc=Symbol.for("react.context"),Nc=Symbol.for("react.server_context"),hd=Symbol.for("react.forward_ref"),id=Symbol.for("react.suspense"),jd=Symbol.for("react.suspense_list"),kd=Symbol.for("react.memo"),ld=Symbol.for("react.lazy"),md=Symbol.for("react.scope"),nd=Symbol.for("react.debug_trace_mode"),od=Symbol.for("react.offscreen"),
pd=Symbol.for("react.legacy_hidden"),qd=Symbol.for("react.cache"),rd=Symbol.for("react.default_value"),sd=Symbol.iterator;
function td(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Ic:return"Fragment";case Hc:return"Portal";case Kc:return"Profiler";case Jc:return"StrictMode";case id:return"Suspense";case jd:return"SuspenseList";case qd:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Mc:return(a.displayName||"Context")+".Consumer";case Lc:return(a._context.displayName||"Context")+".Provider";case hd:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case kd:return b=a.displayName||null,null!==b?b:td(a.type)||"Memo";case ld:b=a._payload;a=a._init;try{return td(a(b))}catch(c){}}return null}var ud={};function vd(a,b){a=a.contextTypes;if(!a)return ud;var c={},d;for(d in a)c[d]=b[d];return c}var wd=null;
function xd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");xd(a,c)}b.context._currentValue2=b.value}}function yd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&yd(a)}
function zd(a){var b=a.parent;null!==b&&zd(b);a.context._currentValue2=a.value}function Ad(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?xd(a,b):Ad(a,b)}
function Bd(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?xd(a,c):Bd(a,c);b.context._currentValue2=b.value}function Cd(a){var b=wd;b!==a&&(null===b?zd(a):null===a?yd(b):b.depth===a.depth?xd(b,a):b.depth>a.depth?Ad(b,a):Bd(b,a),wd=a)}
var Dd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Ed(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Dd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:r({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Dd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=r({},f,h)):r(f,h))}a.state=f}else f.queue=null}
var Fd={id:1,overflow:""};function Gd(a,b,c){var d=a.id;a=a.overflow;var e=32-Hd(d)-1;d&=~(1<<e);c+=1;var f=32-Hd(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Hd(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Hd=Math.clz32?Math.clz32:Id,Jd=Math.log,Kd=Math.LN2;function Id(a){a>>>=0;return 0===a?32:31-(Jd(a)/Kd|0)|0}var Ld=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Md(){}function Nd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Md,Md),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Od=b;throw Ld;}}var Od=null;
function Pd(){if(null===Od)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Od;Od=null;return a}function Qd(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Rd="function"===typeof Object.is?Object.is:Qd,T=null,Sd=null,Td=null,Ud=null,Vd=null,V=null,Wd=!1,Xd=!1,Yd=0,Zd=0,$d=-1,ae=0,be=null,ce=null,de=0;
function ee(){if(null===T)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return T}
function fe(){if(0<de)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function ge(){null===V?null===Vd?(Wd=!1,Vd=V=fe()):(Wd=!0,V=Vd):null===V.next?(Wd=!1,V=V.next=fe()):(Wd=!0,V=V.next);return V}function he(a,b,c,d){for(;Xd;)Xd=!1,Zd=Yd=0,$d=-1,ae=0,de+=1,V=null,c=a(b,d);ie();return c}function je(){var a=be;be=null;return a}function ie(){Ud=Td=Sd=T=null;Xd=!1;Vd=null;de=0;V=ce=null}
function ke(a,b){return"function"===typeof b?b(a):b}function le(a,b,c){T=ee();V=ge();if(Wd){var d=V.queue;b=d.dispatch;if(null!==ce&&(c=ce.get(d),void 0!==c)){ce.delete(d);d=V.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);V.memoizedState=d;return[d,b]}return[V.memoizedState,b]}a=a===ke?"function"===typeof b?b():b:void 0!==c?c(b):b;V.memoizedState=a;a=V.queue={last:null,dispatch:null};a=a.dispatch=me.bind(null,T,a);return[V.memoizedState,a]}
function ne(a,b){T=ee();V=ge();b=void 0===b?null:b;if(null!==V){var c=V.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Rd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();V.memoizedState=[a,b];return a}
function me(a,b,c){if(25<=de)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===T)if(Xd=!0,a={action:c,next:null},null===ce&&(ce=new Map),c=ce.get(b),void 0===c)ce.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function oe(){throw Error("startTransition cannot be called during server rendering.");}function pe(){throw Error("Cannot update optimistic state while rendering.");}
function qe(a){var b=ae;ae+=1;null===be&&(be=[]);return Nd(be,a,b)}function re(){throw Error("Cache cannot be refreshed during server rendering.");}function se(){}
var ue={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return qe(a);if(a.$$typeof===Mc||a.$$typeof===Nc)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){ee();return a._currentValue2},useMemo:ne,useReducer:le,useRef:function(a){T=ee();V=ge();var b=V.memoizedState;return null===b?(a={current:a},V.memoizedState=a):b},useState:function(a){return le(ke,a)},
useInsertionEffect:se,useLayoutEffect:se,useCallback:function(a,b){return ne(function(){return a},b)},useImperativeHandle:se,useEffect:se,useDebugValue:se,useDeferredValue:function(a){ee();return a},useTransition:function(){ee();return[!1,oe]},useId:function(){var a=Sd.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Hd(a)-1)).toString(32)+b;var c=te;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=Yd++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return re},useHostTransitionStatus:function(){ee();return Na},useOptimistic:function(a){ee();return[a,pe]},useFormState:function(a,b,c){ee();var d=Zd++,e=Td;if("function"===typeof a.$$FORM_ACTION){var f=null,g=Ud;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;
if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+ka(JSON.stringify([g,null,d]),0),k===f&&($d=d,b=e[0]))}var l=a.bind(null,b);a=function(q){l(q)};"function"===typeof l.$$FORM_ACTION&&(a.$$FORM_ACTION=function(q){q=l.$$FORM_ACTION(q);void 0!==c&&(c+="",q.action=c);var m=q.data;m&&(null===f&&(f=void 0!==c?"p"+c:"k"+ka(JSON.stringify([g,null,d]),0)),m.append("$ACTION_KEY",f));return q});return[b,a]}var n=a.bind(null,b);return[b,function(q){n(q)}]}},te=null,ve=
{getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},we=Ma.ReactCurrentDispatcher,xe=Ma.ReactCurrentCache;function ye(a){console.error(a);return null}function Be(){}
function Ce(a,b,c,d,e,f,g,h,k,l,n,q){Oa.current=lb;var m=[],w=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:w,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?ye:f,onPostpone:void 0===n?Be:n,onAllReady:void 0===g?
Be:g,onShellReady:void 0===h?Be:h,onShellError:void 0===k?Be:k,onFatalError:void 0===l?Be:l,formState:void 0===q?null:q};c=De(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Ee(b,null,a,-1,null,c,w,null,d,ud,null,Fd);m.push(a);return b}var S=null;function Fe(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,Ge(a))}
function He(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Ee(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var m={replay:null,node:c,childIndex:d,ping:function(){return Fe(a,m)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}
function Ie(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var m={replay:c,node:d,childIndex:e,ping:function(){return Fe(a,m)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}function De(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function W(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function Je(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Ke(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((td(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=r({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function Le(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Gd(c,1,0),Me(a,b,d,-1),b.treeContext=c):h?Me(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function Ne(a,b){if(a&&a.defaultProps){b=r({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Oe(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=vd(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:d);Ed(h,e,f,d);Ke(a,b,c,h,e)}else{h=vd(e,b.legacyContext);T={};Sd=b;Td=a;Ud=c;Zd=Yd=0;$d=-1;ae=0;be=d;d=e(f,h);d=he(e,f,d,h);g=0!==Yd;var k=Zd,l=$d;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Ed(d,e,f,h),Ke(a,b,c,d,e)):Le(a,b,c,d,g,k,l)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=zb(h,e,f),b.keyPath=c,Me(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Sb(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=zb(h,e,f);b.keyPath=c;Me(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(Nb(e))}d.lastPushedText=!1}else{switch(e){case pd:case nd:case Jc:case Kc:case Ic:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case od:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case jd:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case md:throw Error("ReactDOMServer does not yet support scope components.");
case id:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Me(a,b,c,-1)}finally{b.keyPath=e}}else{l=b.keyPath;e=b.blockedBoundary;var n=b.blockedSegment;d=f.fallback;var q=f.children;f=new Set;g=He(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=De(a,n.chunks.length,g,b.formatContext,!1,!1);n.children.push(k);n.lastPushedText=!1;var m=De(a,0,null,b.formatContext,!1,!1);m.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=m;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(Me(a,b,q,-1),a.renderState.generateStaticMarkup||m.lastPushedText&&m.textEmbedded&&m.chunks.push("\x3c!-- --\x3e"),m.status=1,Pe(g,m),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(w){m.status=4,g.status=4,h=W(a,w),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=n,b.keyPath=l}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;null!==l&&(n=[h[1],h[2],[],null],l.workingMap.set(h,n),5===g.status?l.workingMap.get(c)[4]=
n:g.trackedFallbackNode=n);b=Ee(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case hd:e=e.render;T={};Sd=b;Td=a;Ud=c;Zd=Yd=0;$d=-1;ae=0;be=d;d=e(f,g);f=he(e,f,d,g);Le(a,b,c,f,0!==Yd,Zd,$d);return;case kd:e=e.type;f=Ne(e,f);Oe(a,b,c,d,e,f,g);return;case Lc:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue2;e._currentValue2=f;k=wd;wd=f={parent:k,depth:null===k?0:k.depth+1,
context:e,parentValue:g,value:f};b.context=f;b.keyPath=c;Z(a,b,null,h,-1);a=wd;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue2=c===rd?a.context._defaultValue:c;a=wd=a.parent;b.context=a;b.keyPath=d;return;case Mc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;Z(a,b,null,f,-1);b.keyPath=e;return;case ld:h=e._init;e=h(e._payload);f=Ne(e,f);Oe(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+
((null==e?e:typeof e)+"."));}}function Qe(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=De(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Me(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(Pe(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Qe(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case Gc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=td(f),n=null==g?-1===e?0:e:g;g=[b.keyPath,l,n];if(null!==b.replay)a:{var q=b.replay;e=q.nodes;for(d=0;d<e.length;d++){var m=e[d];if(n===m[1]){if(4===m.length){if(null!==l&&l!==m[0])throw Error("Expected the resume to render <"+m[0]+"> in this slot but instead it rendered <"+
l+">. The tree doesn't match so React will fallback to client rendering.");l=m[2];m=m[3];n=b.node;b.replay={nodes:l,slots:m,pendingTasks:1};try{Oe(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(p){if("object"===typeof p&&null!==p&&(p===Ld||"function"===typeof p.then))throw b.node===n&&(b.replay=q),p;b.replay.pendingTasks--;
g=a;a=b.blockedBoundary;c=p;h=W(g,c);Re(g,a,l,m,c,h)}b.replay=q}else{if(f!==id)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(td(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{q=void 0;c=m[5];f=m[2];k=m[3];l=null===m[4]?[]:m[4][2];m=null===m[4]?null:m[4][3];n=b.keyPath;var w=b.replay,C=b.blockedBoundary,N=h.children;h=h.fallback;var u=new Set,t=He(a,u);t.parentFlushed=!0;t.rootSegmentID=c;b.blockedBoundary=
t;b.replay={nodes:f,slots:k,pendingTasks:1};a.renderState.boundaryResources=t.resources;try{Me(a,b,N,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===t.pendingTasks&&0===t.status){t.status=1;a.completedBoundaries.push(t);break b}}catch(p){t.status=4,q=W(a,p),t.errorDigest=q,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(t)}finally{a.renderState.boundaryResources=
C?C.resources:null,b.blockedBoundary=C,b.replay=w,b.keyPath=n}b=Ie(a,null,{nodes:l,slots:m,pendingTasks:0},h,-1,C,u,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else Oe(a,b,g,c,f,h,k);return;case Hc:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case ld:h=d._init;d=h(d._payload);Z(a,b,null,d,e);return}if(La(d)){Se(a,
b,d,e);return}null===d||"object"!==typeof d?h=null:(h=sd&&d[sd]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);Se(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,null,qe(d),e);if(d.$$typeof===Mc||d.$$typeof===Nc)return Z(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+
"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Fc(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Fc(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function Se(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{Se(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(q){if("object"===typeof q&&
null!==q&&(q===Ld||"function"===typeof q.then))throw q;b.replay.pendingTasks--;c=a;var l=b.blockedBoundary,n=q;a=W(c,n);Re(c,l,d,k,n,a)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)k=c[d],b.treeContext=Gd(f,g,d),l=h[d],"number"===typeof l?(Qe(a,b,l,k,d),delete h[d]):Me(a,b,k,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Gd(f,g,h),Me(a,b,d,h);b.treeContext=
f;b.keyPath=e}
function Me(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.blockedSegment;if(null===l)try{return Z(a,b,null,c,d)}catch(m){if(ie(),c=m===Ld?Pd():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=je();a=Ie(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Cd(g);return}}else{var n=
l.children.length,q=l.chunks.length;try{return Z(a,b,null,c,d)}catch(m){if(ie(),l.children.length=n,l.chunks.length=q,c=m===Ld?Pd():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=je();l=b.blockedSegment;n=De(a,l.chunks.length,null,b.formatContext,l.lastPushedText,!0);l.children.push(n);l.lastPushedText=!1;a=Ee(a,d,b.node,b.childIndex,b.blockedBoundary,n,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;Cd(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Cd(g);throw c;}function Te(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,Ue(this,b,a))}
function Re(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Re(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,n=He(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=l;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var q in d)delete d[q]}}
function Ve(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){W(b,c);Je(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=W(b,c),Re(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&We(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=W(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return Ve(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&Xe(b)}
function Ye(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),l=k.next();0<e.remainingCapacity&&!l.done;l=k.next()){var n=l.value,q=n.props,m=q.href,w=n.props,C=Tb(w.href,"style",{crossOrigin:w.crossOrigin,integrity:w.integrity,
nonce:w.nonce,type:w.type,fetchPriority:w.fetchPriority,referrerPolicy:w.referrerPolicy,media:w.media});if(2<=(e.remainingCapacity-=C.length))c.resets.style[m]=B,f&&(f+=", "),f+=C,c.resets.style[m]="string"===typeof q.crossOrigin||"string"===typeof q.integrity?[q.crossOrigin,q.integrity]:B;else break b}}f?d({Link:f}):d({})}}}catch(N){W(a,N)}}function We(a){null===a.trackedPostpones&&Ye(a,!0);a.onShellError=Be;a=a.onShellReady;a()}
function Xe(a){Ye(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function Pe(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Pe(a,c)}else a.completedSegments.push(b)}
function Ue(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&We(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&Pe(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(Te,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(Pe(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&Xe(a)}
function Ge(a){if(2!==a.status){var b=wd,c=we.current;we.current=ue;var d=xe.current;xe.current=ve;var e=S;S=a;var f=te;te=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,n=k.blockedBoundary;l.renderState.boundaryResources=n?n.resources:null;var q=k.blockedSegment;if(null===q){var m=l;if(0!==k.replay.pendingTasks){Cd(k.context);try{var w=k.thenableState;k.thenableState=null;Z(m,k,w,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);Ue(m,k.blockedBoundary,null)}catch(P){ie();var C=P===Ld?Pd():P;if("object"===typeof C&&null!==C&&"function"===typeof C.then){var N=k.ping;C.then(N,N);k.thenableState=je()}else{k.replay.pendingTasks--;k.abortSet.delete(k);l=void 0;var u=m,t=k.blockedBoundary,p=C,F=k.replay.nodes,x=k.replay.slots;l=W(u,p);Re(u,t,F,x,p,l);m.pendingRootTasks--;0===m.pendingRootTasks&&We(m);m.allPendingTasks--;0===m.allPendingTasks&&Xe(m)}}finally{m.renderState.boundaryResources=
null}}}else if(m=void 0,u=q,0===u.status){Cd(k.context);var v=u.children.length,D=u.chunks.length;try{var O=k.thenableState;k.thenableState=null;Z(l,k,O,k.node,k.childIndex);l.renderState.generateStaticMarkup||u.lastPushedText&&u.textEmbedded&&u.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);u.status=1;Ue(l,k.blockedBoundary,u)}catch(P){ie();u.children.length=v;u.chunks.length=D;var A=P===Ld?Pd():P;if("object"===typeof A&&null!==A&&"function"===typeof A.then){var ea=k.ping;A.then(ea,ea);k.thenableState=
je()}else{k.abortSet.delete(k);u.status=4;var U=k.blockedBoundary;m=W(l,A);null===U?Je(l,A):(U.pendingTasks--,4!==U.status&&(U.status=4,U.errorDigest=m,U.parentFlushed&&l.clientRenderedBoundaries.push(U)));l.allPendingTasks--;0===l.allPendingTasks&&Xe(l)}}finally{l.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&Ze(a,a.destination)}catch(P){W(a,P),Je(a,P)}finally{te=f,we.current=c,xe.current=d,c===ue&&Cd(b),S=e}}}
function $e(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=af(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function af(a,b,c){var d=c.boundary;if(null===d)return $e(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=z(d),b.push(d),b.push('"')),b.push("></template>")),$e(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),ec(b,a.renderState,
d.rootSegmentID),$e(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),ec(b,a.renderState,d.rootSegmentID),$e(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(Cc,e),c.stylesheets.forEach(Dc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
af(a,b,c[0]);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function bf(a,b,c){fc(b,a.renderState,c.parentFormatContext,c.id);af(a,b,c);return gc(b,c.parentFormatContext)}
function cf(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)df(a,b,c,d[e]);d.length=0;pc(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),vc(b,c)):(b.push('" data-sty="'),wc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Vb(b,a)&&d}
function df(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return bf(a,b,d)}if(e===c.rootSegmentID)return bf(a,b,d);bf(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function Ze(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,n=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(n)for(f=0;f<n.length;f++)b.push(n[f]);else{var q=M("head");b.push(q);
b.push(">")}}else if(n)for(f=0;f<n.length;f++)b.push(n[f]);var m=e.charsetChunks;for(f=0;f<m.length;f++)b.push(m[f]);m.length=0;e.preconnects.forEach(Q,b);e.preconnects.clear();var w=e.preconnectChunks;for(f=0;f<w.length;f++)b.push(w[f]);w.length=0;e.fontPreloads.forEach(Q,b);e.fontPreloads.clear();e.highImagePreloads.forEach(Q,b);e.highImagePreloads.clear();e.styles.forEach(sc,b);var C=e.importMapChunks;for(f=0;f<C.length;f++)b.push(C[f]);C.length=0;e.bootstrapScripts.forEach(Q,b);e.scripts.forEach(Q,
b);e.scripts.clear();e.bulkPreloads.forEach(Q,b);e.bulkPreloads.clear();var N=e.preloadChunks;for(f=0;f<N.length;f++)b.push(N[f]);N.length=0;var u=e.hoistableChunks;for(f=0;f<u.length;f++)b.push(u[f]);u.length=0;if(l&&null===n){var t=Nb("head");b.push(t)}af(a,b,d);a.completedRootSegment=null;Vb(b,a.renderState)}else return;var p=a.renderState;d=0;p.preconnects.forEach(Q,b);p.preconnects.clear();var F=p.preconnectChunks;for(d=0;d<F.length;d++)b.push(F[d]);F.length=0;p.fontPreloads.forEach(Q,b);p.fontPreloads.clear();
p.highImagePreloads.forEach(Q,b);p.highImagePreloads.clear();p.styles.forEach(uc,b);p.scripts.forEach(Q,b);p.scripts.clear();p.bulkPreloads.forEach(Q,b);p.bulkPreloads.clear();var x=p.preloadChunks;for(d=0;d<x.length;d++)b.push(x[d]);x.length=0;var v=p.hoistableChunks;for(d=0;d<v.length;d++)b.push(v[d]);v.length=0;var D=a.clientRenderedBoundaries;for(c=0;c<D.length;c++){var O=D[c];p=b;var A=a.resumableState,ea=a.renderState,U=O.rootSegmentID,P=O.errorDigest,ma=O.errorMessage,fa=O.errorComponentStack,
X=0===A.streamingFormat;X?(p.push(ea.startInlineScript),0===(A.instructions&4)?(A.instructions|=4,p.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):p.push('$RX("')):p.push('<template data-rxi="" data-bid="');p.push(ea.boundaryPrefix);var Pa=U.toString(16);p.push(Pa);X&&p.push('"');if(P||ma||fa)if(X){p.push(",");var Qa=ic(P||"");p.push(Qa)}else{p.push('" data-dgst="');
var Ra=z(P||"");p.push(Ra)}if(ma||fa)if(X){p.push(",");var na=ic(ma||"");p.push(na)}else{p.push('" data-msg="');var R=z(ma||"");p.push(R)}if(fa)if(X){p.push(",");var qb=ic(fa);p.push(qb)}else{p.push('" data-stck="');var oa=z(fa);p.push(oa)}if(X?!p.push(")\x3c/script>"):!p.push('"></template>')){a.destination=null;c++;D.splice(0,c);return}}D.splice(0,c);var pa=a.completedBoundaries;for(c=0;c<pa.length;c++)if(!cf(a,b,pa[c])){a.destination=null;c++;pa.splice(0,c);return}pa.splice(0,c);var ba=a.partialBoundaries;
for(c=0;c<ba.length;c++){var qa=ba[c];a:{D=a;O=b;D.renderState.boundaryResources=qa.resources;var ra=qa.completedSegments;for(A=0;A<ra.length;A++)if(!df(D,O,qa,ra[A])){A++;ra.splice(0,A);var Sa=!1;break a}ra.splice(0,A);Sa=pc(O,qa.resources,D.renderState)}if(!Sa){a.destination=null;c++;ba.splice(0,c);return}}ba.splice(0,c);var sa=a.completedBoundaries;for(c=0;c<sa.length;c++)if(!cf(a,b,sa[c])){a.destination=null;c++;sa.splice(0,c);return}sa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&
0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(ba=Nb("body"),b.push(ba)),c.hasHtml&&(c=Nb("html"),b.push(c)),b.push(null),a.destination=null)}}function ef(a){a.flushScheduled=null!==a.destination;Ge(a);null===a.trackedPostpones&&Ye(a,0===a.pendingRootTasks)}function zc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?Ze(a,b):a.flushScheduled=!1}}
function ff(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Ze(a,b)}catch(c){W(a,c),Je(a,c)}}}function gf(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return Ve(e,a,d)});c.clear()}null!==a.destination&&Ze(a,a.destination)}catch(e){W(a,e),Je(a,e)}}function hf(){}
function jf(a,b,c,d){var e=!1,f=null,g="",h=!1;b=ob(b?b.identifierPrefix:void 0,void 0);a=Ce(a,b,Ec(b,c),pb(),Infinity,hf,void 0,function(){h=!0},void 0,void 0,void 0);ef(a);gf(a,d);ff(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");return g}
function kf(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var lf=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}kf(b,a);var c=b.prototype;c._destroy=function(d,e){gf(this.request);e(d)};c._read=function(){this.startedFlowing&&ff(this.request,this)};return b}(ja.Readable);function mf(){}
function nf(a,b){var c=new lf;b=ob(b?b.identifierPrefix:void 0,void 0);var d=Ce(a,b,Ec(b,!1),pb(),Infinity,mf,function(){c.startedFlowing=!0;ff(d,c)},void 0,void 0,void 0);c.request=d;ef(d);return c}exports.renderToNodeStream=function(a,b){return nf(a,b)};exports.renderToStaticMarkup=function(a,b){return jf(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return nf(a,b)};exports.renderToString=function(a,b){return jf(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-2c338b16f-20231116";

//# sourceMappingURL=react-dom-server-legacy.node.production.min.js.map
