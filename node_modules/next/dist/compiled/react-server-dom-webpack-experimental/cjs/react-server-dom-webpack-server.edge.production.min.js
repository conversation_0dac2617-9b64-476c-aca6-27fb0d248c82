/*
 React
 react-server-dom-webpack-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react"),ba=require("react-dom"),m=null,n=0;function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<n&&(a.enqueue(new Uint8Array(m.buffer,0,n)),m=new Uint8Array(512),n=0),a.enqueue(b);else{var d=m.length-n;d<b.byteLength&&(0===d?a.enqueue(m):(m.set(b.subarray(0,d),n),a.enqueue(m),b=b.subarray(d)),m=new Uint8Array(512),n=0);m.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function u(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:fa}})}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=u({},a.$$id,!0),e=new Proxy(c,ka);a.status="fulfilled";a.value=e;return a.then=u(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}c=a[b];c||(c=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},sa={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=v();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),w(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?w(d,"C",[a,b]):w(d,"C",a))}}}
function na(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,h=d.imageSizes,k="";"string"===typeof g&&""!==g?(k+="["+g+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;f+="[image]"+k}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=x(d))?w(c,"L",[a,b,d]):w(c,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"m",[a,b]):w(d,"m",a)}}}
function pa(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=x(d))?w(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?w(c,"S",[a,b]):w(c,"S",a)}}}function qa(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"X",[a,b]):w(d,"X",a)}}}function ra(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"M",[a,b]):w(d,"M",a)}}}
function x(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ta=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,va="function"===typeof AsyncLocalStorage,wa=va?new AsyncLocalStorage:null,y=Symbol.for("react.element"),xa=Symbol.for("react.fragment"),ya=Symbol.for("react.provider"),za=Symbol.for("react.server_context"),Aa=Symbol.for("react.forward_ref"),Ba=Symbol.for("react.suspense"),Ca=Symbol.for("react.suspense_list"),Da=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),B=Symbol.for("react.default_value"),Ea=Symbol.for("react.memo_cache_sentinel"),
C=Symbol.for("react.postpone"),Fa=Symbol.iterator,D=null;function Ga(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Ga(a,d);b.context._currentValue=b.value}}}function Ha(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ha(a)}
function Ia(a){var b=a.parent;null!==b&&Ia(b);a.context._currentValue=a.value}function Ja(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Ga(a,b):Ja(a,b)}
function Ka(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?Ga(a,d):Ka(a,d);b.context._currentValue=b.value}function La(a){var b=D;b!==a&&(null===b?Ia(a):null===a?Ha(b):b.depth===a.depth?Ga(b,a):b.depth>a.depth?Ja(b,a):Ka(b,a),D=a)}function Ma(a,b){var d=a._currentValue;a._currentValue=b;var c=D;return D=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Na=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Oa(){}function Pa(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Oa,Oa),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Qa=b;throw Na;}}var Qa=null;
function Ra(){if(null===Qa)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Qa;Qa=null;return a}var E=null,Sa=0,F=null;function Ta(){var a=F;F=null;return a}function Ua(a){return a._currentValue}
var Ya={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:H,useTransition:H,readContext:Ua,useContext:Ua,useReducer:H,useRef:H,useState:H,useInsertionEffect:H,useLayoutEffect:H,useImperativeHandle:H,useEffect:H,useId:Va,useSyncExternalStore:H,useCacheRefresh:function(){return Wa},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ea;return b},use:Xa};
function H(){throw Error("This Hook is not supported in Server Components.");}function Wa(){throw Error("Refreshing the cache is not supported in Server Components.");}function Va(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function Xa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Sa;Sa+=1;null===F&&(F=[]);return Pa(F,a,b)}if(a.$$typeof===za)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Za(){return(new AbortController).signal}function $a(){var a=v();return a?a.cache:new Map}
var ab={getCacheSignal:function(){var a=$a(),b=a.get(Za);void 0===b&&(b=Za(),a.set(Za,b));return b},getCacheForType:function(a){var b=$a(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},bb=Array.isArray,cb=Object.getPrototypeOf;function db(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function eb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(bb(a))return"[...]";a=db(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function fb(a){if("string"===typeof a)return a;switch(a){case Ba:return"Suspense";case Ca:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Aa:return fb(a.render);case Da:return fb(a.type);case z:var b=a._payload;a=a._init;try{return fb(a(b))}catch(d){}}return""}
function I(a,b){var d=db(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(bb(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?I(g):eb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===y)e="<"+fb(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var h=f[g],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k="object"===typeof k&&null!==k?I(k):
eb(k);h===b?(d=e.length,c=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var gb=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,hb=gb.ContextRegistry,J=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!J)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var ib=Object.prototype,K=JSON.stringify,jb=J.TaintRegistryObjects,L=J.TaintRegistryValues,kb=J.TaintRegistryByteLengths,lb=J.TaintRegistryPendingRequests,mb=J.ReactCurrentCache,nb=gb.ReactCurrentDispatcher;function M(a){throw Error(a);}
function ob(a){a=a.taintCleanupQueue;lb.delete(a);for(var b=0;b<a.length;b++){var d=a[b],c=L.get(d);void 0!==c&&(1===c.count?L.delete(d):c.count--)}a.length=0}function pb(a){console.error(a)}function qb(){}
function rb(a,b,d,c,e,f){if(null!==mb.current&&mb.current!==ab)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=sa;mb.current=ab;var g=new Set,h=[],k=[];lb.add(k);var l=new Set,A={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:l,abortableTasks:g,pingedTasks:h,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:k,onError:void 0===d?pb:d,onPostpone:void 0===f?qb:f,toJSON:function(ua,G){return sb(A,this,ua,G)}};A.pendingChunks++;b=tb(c);a=ub(A,a,b,g);h.push(a);return A}var N=null;function v(){if(N)return N;if(va){var a=wa.getStore();if(a)return a}return null}var vb={};
function wb(a,b){a.pendingChunks++;var d=ub(a,null,D,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,xb(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===C?(yb(a,c.message),zb(a,d.id)):(c=O(a,c),P(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;xb(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=O(a,e);P(a,d.id,e);null!==a.destination&&Q(a,a.destination)});return d.id}function w(a,b,d){d=K(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);Ab(a)}function Bb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function Cb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:z,_payload:a,_init:Bb}}
function R(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[y,b,d,e];Sa=0;F=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:Cb(e):e}if("string"===typeof b)return[y,b,d,e];if("symbol"===typeof b)return b===xa?e.children:[y,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[y,b,d,e];switch(b.$$typeof){case z:var g=
b._init;b=g(b._payload);return R(a,b,d,c,e,f);case Aa:return a=b.render,Sa=0,F=f,a(e,void 0);case Da:return R(a,b.type,d,c,e,f);case ya:return Ma(b._context,e.value),[y,b,d,{value:e.value,children:e.children,__pop:vb}]}}throw Error("Unsupported Server Component type: "+eb(b));}function xb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return Db(a)},0))}
function ub(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return xb(a,e)},thenableState:null};c.add(e);return e}function S(a){return"$"+a.toString(16)}function Eb(a,b,d){a=K(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function Fb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===y&&"1"===d?"$L"+g.toString(16):S(g);try{var h=a.bundlerConfig,k=c.$$id;g="";var l=h[k];if(l)g=l.name;else{var A=k.lastIndexOf("#");-1!==A&&(g=k.slice(A+1),l=h[k.slice(0,A)]);if(!l)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var ua=!0===c.$$async?[l.id,l.chunks,g,1]:[l.id,l.chunks,
g];a.pendingChunks++;var G=a.nextChunkId++,Yb=K(ua),Zb=G.toString(16)+":I"+Yb+"\n",$b=q.encode(Zb);a.completedImportChunks.push($b);f.set(e,G);return b[0]===y&&"1"===d?"$L"+G.toString(16):S(G)}catch(ac){return a.pendingChunks++,b=a.nextChunkId++,d=O(a,ac),P(a,b,d),S(b)}}function T(a,b){a.pendingChunks++;b=ub(a,b,D,a.abortableTasks);Gb(a,b);return b.id}
function U(a,b,d){if(kb.has(d.byteLength)){var c=L.get(String.fromCharCode.apply(String,new Uint8Array(d.buffer,d.byteOffset,d.byteLength)));void 0!==c&&M(c.message)}a.pendingChunks+=2;c=a.nextChunkId++;var e=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);d=512<d.byteLength?e.slice():e;e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";b=q.encode(b);a.completedRegularChunks.push(b,d);return S(c)}var V=!1;
function sb(a,b,d,c){switch(c){case y:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===y||c.$$typeof===z);)try{switch(c.$$typeof){case y:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=T(a,c);return S(g)}if(V===c)V=null;else return S(f)}else e.set(c,-1);var h=c;c=R(a,h.type,h.key,h.ref,h.props,null);break;case z:var k=c._init;c=k(c._payload)}}catch(l){d=l===Na?Ra():l;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=ub(a,c,D,a.abortableTasks),
c=a.ping,d.then(c,c),a.thenableState=Ta(),"$L"+a.id.toString(16);if(d.$$typeof===C)return c=d,a.pendingChunks++,d=a.nextChunkId++,yb(a,c.message),zb(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=O(a,d);P(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){e=jb.get(c);void 0!==e&&M(e);if(c.$$typeof===r)return Fb(a,b,d,c);b=a.writtenObjects;e=b.get(c);if("function"===typeof c.then){if(void 0!==e)if(V===c)V=null;else return"$@"+e.toString(16);a=wb(a,c);b.set(c,
a);return"$@"+a.toString(16)}if(c.$$typeof===ya)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=Eb(a,d,"$P"+c),a.completedRegularChunks.push(c)),S(d);if(c===vb){a=D;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===B?a.context._defaultValue:c;D=a.parent;return}if(void 0!==e){if(-1===e)return a=T(a,c),S(a);if(V===c)V=null;else return S(e)}else b.set(c,
-1);if(bb(c))return c;if(c instanceof Map){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d][0],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$Q"+T(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$W"+T(a,c).toString(16)}if(c instanceof ArrayBuffer)return U(a,"A",new Uint8Array(c));if(c instanceof Int8Array)return U(a,"C",c);if(c instanceof
Uint8Array)return U(a,"c",c);if(c instanceof Uint8ClampedArray)return U(a,"U",c);if(c instanceof Int16Array)return U(a,"S",c);if(c instanceof Uint16Array)return U(a,"s",c);if(c instanceof Int32Array)return U(a,"L",c);if(c instanceof Uint32Array)return U(a,"l",c);if(c instanceof Float32Array)return U(a,"F",c);if(c instanceof Float64Array)return U(a,"D",c);if(c instanceof BigInt64Array)return U(a,"N",c);if(c instanceof BigUint64Array)return U(a,"m",c);if(c instanceof DataView)return U(a,"V",c);null===
c||"object"!==typeof c?a=null:(a=Fa&&c[Fa]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=cb(c);if(a!==ib&&(null===a||null!==cb(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){e=L.get(c);void 0!==e&&M(e.message);if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,
c=q.encode(c),b=c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",b=q.encode(b),a.completedRegularChunks.push(b,c),S(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){e=jb.get(c);void 0!==e&&M(e);if(c.$$typeof===r)return Fb(a,b,d,c);if(c.$$typeof===t)return d=a.writtenServerReferences,
b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=T(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+I(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+I(b,d));}if("symbol"===typeof c){e=
a.writtenSymbols;f=e.get(c);if(void 0!==f)return S(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+I(b,d));a.pendingChunks++;d=a.nextChunkId++;b=Eb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return S(d)}if("bigint"===typeof c)return a=L.get(c),void 0!==a&&M(a.message),"$n"+c.toString(10);throw Error("Type "+typeof c+
" is not supported in Client Component props."+I(b,d));}function yb(a,b){a=a.onPostpone;a(b)}function O(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}
function Hb(a,b){ob(a);null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}function zb(a,b){b=b.toString(16)+":P\n";b=q.encode(b);a.completedErrorChunks.push(b)}function P(a,b,d){d={digest:d};b=b.toString(16)+":E"+K(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}
function Gb(a,b){if(0===b.status){La(b.context);try{var d=b.model;if("object"===typeof d&&null!==d&&d.$$typeof===y){a.writtenObjects.set(d,b.id);var c=d,e=b.thenableState;b.model=d;d=R(a,c.type,c.key,c.ref,c.props,e);for(b.thenableState=null;"object"===typeof d&&null!==d&&d.$$typeof===y;)a.writtenObjects.set(d,b.id),c=d,b.model=d,d=R(a,c.type,c.key,c.ref,c.props,null)}"object"===typeof d&&null!==d&&a.writtenObjects.set(d,b.id);var f=b.id;V=d;var g=K(d,a.toJSON),h=f.toString(16)+":"+g+"\n",k=q.encode(h);
a.completedRegularChunks.push(k);a.abortableTasks.delete(b);b.status=1}catch(l){f=l===Na?Ra():l;if("object"===typeof f&&null!==f){if("function"===typeof f.then){a=b.ping;f.then(a,a);b.thenableState=Ta();return}if(f.$$typeof===C){a.abortableTasks.delete(b);b.status=4;yb(a,f.message);zb(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;f=O(a,f);P(a,b.id,f)}}}
function Db(a){var b=nb.current;nb.current=Ya;var d=N;E=N=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)Gb(a,c[e]);null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Hb(a,f)}finally{nb.current=b,E=null,N=d}}
function Q(a,b){m=new Uint8Array(512);n=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,p(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)p(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,p(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,p(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,m&&0<n&&(b.enqueue(new Uint8Array(m.buffer,0,n)),m=null,n=0)}0===a.pendingChunks&&
(ob(a),b.close())}function Ib(a){a.flushScheduled=null!==a.destination;va?setTimeout(function(){return wa.run(a,Db,a)},0):setTimeout(function(){return Db(a)},0)}function Ab(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return Q(a,b)},0)}}
function Jb(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===C)yb(a,b.message),zb(a,c,b);else{var e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=O(a,e);P(a,c,f,e)}d.forEach(function(g){g.status=3;var h=S(c);g=Eb(a,g.id,h);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Hb(a,g)}}
function tb(a){if(a){var b=D;La(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!hb[e]){var f={$$typeof:za,_currentValue:B,_currentValue2:B,_defaultValue:B,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:ya,_context:f};hb[e]=f}Ma(hb[e],c)}a=D;La(b);return a}return null}
function Kb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var Lb=new Map;
function Mb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Nb(){}
function Ob(a){for(var b=a[1],d=[],c=0;c<b.length;){var e=b[c++];b[c++];var f=Lb.get(e);if(void 0===f){f=__webpack_chunk_load__(e);d.push(f);var g=Lb.set.bind(Lb,e,null);f.then(g,Nb);Lb.set(e,f)}else null!==f&&d.push(f)}return 4===a.length?0===d.length?Mb(a[0]):Promise.all(d).then(function(){return Mb(a[0])}):0<d.length?Promise.all(d):null}
function W(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Pb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Pb.prototype=Object.create(Promise.prototype);
Pb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Qb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Rb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Sb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Rb(d,b)}}function Tb(a,b,d,c,e,f){var g=Kb(a._bundlerConfig,b);a=Ob(g);if(d)d=Promise.all([d,a]).then(function(h){h=h[0];var k=W(g);return k.bind.apply(k,[null].concat(h))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Ub(c,e,f),Vb(c));return null}var X=null,Y=null;
function Qb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Wb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Sb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Pb("resolved_model",c,null,a):new Pb("pending",null,null,a),d.set(b,c));return c}function Ub(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Rb(e,c.value))}}function Vb(a){return function(b){return Sb(a,b)}}
function Xb(a,b){a=Z(a,b);"resolved_model"===a.status&&Qb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function bc(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Xb(a,c),Tb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Xb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Xb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,h){h.startsWith(e)&&f.append(h.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Qb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Ub(c,b,d),Vb(c)),null;default:throw a.reason;}}return c}
function cc(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?bc(e,this,f,g):g}};return e}function dc(a){Wb(a,Error("Connection closed."))}function ec(a,b,d){var c=Kb(a,b);a=Ob(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}
function fc(a,b,d){a=cc(b,d,a);dc(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=fc(a,b,e),c=ec(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=ec(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(g,h){h.startsWith("$ACTION_REF_")&&(g="$ACTION_"+h.slice(12)+":",e=fc(b,d,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,c,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=cc(b,"",a);b=Z(a,0);dc(a);return b};
exports.registerClientReference=function(a,b,d){return u(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=rb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)Jb(c,e.reason);else{var f=function(){Jb(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){Ib(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===c.destination){c.destination=g;try{Q(c,
g)}catch(h){O(c,h),Hb(c,h)}}},cancel:function(g){c.destination=null;Jb(c,g)}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-webpack-server.edge.production.min.js.map
