{"version": 3, "sources": ["../../src/trace/trace.ts"], "names": ["Span", "trace", "flushAllTraces", "exportTraceState", "initializeTraceState", "getTraceEvents", "recordTraceEvents", "clearTraceEvents", "NUM_OF_MICROSEC_IN_NANOSEC", "BigInt", "count", "getId", "defaultParentSpanId", "shouldSaveTraceEvents", "savedTraceEvents", "SpanStatus", "Started", "Stopped", "constructor", "name", "parentId", "attrs", "startTime", "status", "id", "_start", "process", "hrtime", "bigint", "now", "Date", "stop", "stopTime", "end", "duration", "Number", "MAX_SAFE_INTEGER", "Error", "timestamp", "traceEvent", "tags", "reporter", "report", "push", "<PERSON><PERSON><PERSON><PERSON>", "manualTraceChild", "span", "setAttribute", "key", "value", "traceFn", "fn", "traceAsyncFn", "flushAll", "lastId", "state", "events"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAwBaA,IAAI;eAAJA;;IA6GAC,KAAK;eAALA;;IAQAC,cAAc;eAAdA;;IAKAC,gBAAgB;eAAhBA;;IAKAC,oBAAoB;eAApBA;;IAMGC,cAAc;eAAdA;;IAIAC,iBAAiB;eAAjBA;;IAYHC,gBAAgB;eAAhBA;;;wBA7KY;AAGzB,MAAMC,6BAA6BC,OAAO;AAC1C,IAAIC,QAAQ;AACZ,MAAMC,QAAQ;IACZD;IACA,OAAOA;AACT;AACA,IAAIE;AACJ,IAAIC;AACJ,IAAIC,mBAAiC,EAAE;IAIhC;UAAKC,UAAU;IAAVA,WACVC,aAAU;IADAD,WAEVE,aAAU;GAFAF,eAAAA;AASL,MAAMf;IAWXkB,YAAY,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,SAAS,EAMV,CAAE;QACD,IAAI,CAACH,IAAI,GAAGA;QACZ,IAAI,CAACC,QAAQ,GAAGA,YAAYR;QAC5B,IAAI,CAACS,KAAK,GAAGA,QAAQ;YAAE,GAAGA,KAAK;QAAC,IAAI,CAAC;QACrC,IAAI,CAACE,MAAM,GAjCH;QAkCR,IAAI,CAACC,EAAE,GAAGb;QACV,IAAI,CAACc,MAAM,GAAGH,aAAaI,QAAQC,MAAM,CAACC,MAAM;QAChD,wEAAwE;QACxE,iDAAiD;QACjD,2IAA2I;QAC3I,wDAAwD;QACxD,iFAAiF;QACjF,IAAI,CAACC,GAAG,GAAGC,KAAKD,GAAG;IACrB;IAEA,yEAAyE;IACzE,+DAA+D;IAC/D,wEAAwE;IACxE,yCAAyC;IACzCE,KAAKC,QAAiB,EAAE;QACtB,IAAI,IAAI,CAACT,MAAM,KAhDP,WAgDgC;YACtC,oCAAoC;YACpC,yFAAyF;YACzF;QACF;QACA,MAAMU,MAAcD,YAAYN,QAAQC,MAAM,CAACC,MAAM;QACrD,MAAMM,WAAW,AAACD,CAAAA,MAAM,IAAI,CAACR,MAAM,AAAD,IAAKjB;QACvC,IAAI,CAACe,MAAM,GAvDH;QAwDR,IAAIW,WAAWC,OAAOC,gBAAgB,EAAE;YACtC,MAAM,IAAIC,MAAM,CAAC,4CAA4C,EAAEH,SAAS,CAAC;QAC3E;QACA,MAAMI,YAAY,IAAI,CAACb,MAAM,GAAGjB;QAChC,MAAM+B,aAAyB;YAC7BpB,MAAM,IAAI,CAACA,IAAI;YACfe,UAAUC,OAAOD;YACjBI,WAAWH,OAAOG;YAClBd,IAAI,IAAI,CAACA,EAAE;YACXJ,UAAU,IAAI,CAACA,QAAQ;YACvBoB,MAAM,IAAI,CAACnB,KAAK;YAChBC,WAAW,IAAI,CAACO,GAAG;QACrB;QACAY,gBAAQ,CAACC,MAAM,CAACH;QAChB,IAAI1B,uBAAuB;YACzBC,iBAAiB6B,IAAI,CAACJ;QACxB;IACF;IAEAK,WAAWzB,IAAY,EAAEE,KAAkB,EAAE;QAC3C,OAAO,IAAIrB,KAAK;YAAEmB;YAAMC,UAAU,IAAI,CAACI,EAAE;YAAEH;QAAM;IACnD;IAEAwB,iBACE1B,IAAY,EACZ,yCAAyC;IACzCG,SAAiB,EACjB,wCAAwC;IACxCU,QAAgB,EAChBX,KAAkB,EAClB;QACA,MAAMyB,OAAO,IAAI9C,KAAK;YAAEmB;YAAMC,UAAU,IAAI,CAACI,EAAE;YAAEH;YAAOC;QAAU;QAClEwB,KAAKf,IAAI,CAACC;IACZ;IAEArB,QAAQ;QACN,OAAO,IAAI,CAACa,EAAE;IAChB;IAEAuB,aAAaC,GAAW,EAAEC,KAAa,EAAE;QACvC,IAAI,CAAC5B,KAAK,CAAC2B,IAAI,GAAGC;IACpB;IAEAC,QAAWC,EAAqB,EAAK;QACnC,IAAI;YACF,OAAOA,GAAG,IAAI;QAChB,SAAU;YACR,IAAI,CAACpB,IAAI;QACX;IACF;IAEA,MAAMqB,aAAgBD,EAAkC,EAAc;QACpE,IAAI;YACF,OAAO,MAAMA,GAAG,IAAI;QACtB,SAAU;YACR,IAAI,CAACpB,IAAI;QACX;IACF;AACF;AAEO,MAAM9B,QAAQ,CACnBkB,MACAC,UACAC;IAEA,OAAO,IAAIrB,KAAK;QAAEmB;QAAMC;QAAUC;IAAM;AAC1C;AAEO,MAAMnB,iBAAiB,IAAMuC,gBAAQ,CAACY,QAAQ;AAK9C,MAAMlD,mBAAmB,IAAmB,CAAA;QACjDS;QACA0C,QAAQ5C;QACRG;IACF,CAAA;AACO,MAAMT,uBAAuB,CAACmD;IACnC7C,QAAQ6C,MAAMD,MAAM;IACpB1C,sBAAsB2C,MAAM3C,mBAAmB;IAC/CC,wBAAwB0C,MAAM1C,qBAAqB;AACrD;AAEO,SAASR;IACd,OAAOS;AACT;AAEO,SAASR,kBAAkBkD,MAAoB;IACpD,KAAK,MAAMjB,cAAciB,OAAQ;QAC/Bf,gBAAQ,CAACC,MAAM,CAACH;QAChB,IAAIA,WAAWf,EAAE,GAAGd,OAAO;YACzBA,QAAQ6B,WAAWf,EAAE,GAAG;QAC1B;IACF;IACA,IAAIX,uBAAuB;QACzBC,iBAAiB6B,IAAI,IAAIa;IAC3B;AACF;AAEO,MAAMjD,mBAAmB,IAAOO,mBAAmB,EAAE"}